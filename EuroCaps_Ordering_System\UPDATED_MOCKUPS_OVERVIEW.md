# 🎨 UPDATED MOCKUPS OVERVIEW
## Nieuwe Scherm Mockups Gebaseerd op Use Case Diagrams + Database Integratie

---

## 📋 **OVERZICHT VAN ALLE SCHERMEN DIE MOCKUPS NODIG HEBBEN**

### **✅ BESTAANDE SCHERMEN DIE GEÜPDATET MOETEN WORDEN:**

1. **UC1 - Login Screen** ✅ (Standaard - geen mockup nodig)
2. **UC2 - Dashboard Screen** 🔄 (Update met nieuwe database metrics)
3. **UC3 - Customer List Screen** 🔄 (Update met stakeholder integratie)
4. **UC4 - Product Catalog Screen** 🔄 (Update met material requirements)
5. **UC5 - New Order Screen** 🔄 (Update met material availability)
6. **UC6 - Order Detail Screen** 🔄 (Update met material costs)
7. **UC7 - Order Item Screen** 🔄 (Update met stock levels)
8. **UC8 - Order History Screen** 🔄 (Update met material status)
9. **UC9 - Order Confirmation Screen** 🔄 (Update met delivery tracking)
10. **UC10 - Settings Screen** 🔄 (Update met database connections)

### **🆕 NIEUWE SCHERMEN DIE TOEGEVOEGD MOETEN WORDEN:**

11. **User Registration Screen** 🆕 (Voor nieuwe gebruikers)
12. **Forgot Password Screen** 🆕 (Wachtwoord reset)
13. **Raw Materials Management Screen** 🆕 (Voorraad beheer)
14. **Supplier Management Screen** 🆕 (Leveranciers beheer)
15. **Material Order Screen** 🆕 (Materialen bestellen)
16. **Quality Control Screen** 🆕 (Kwaliteitscontrole)
17. **Production Planning Screen** 🆕 (Productie planning)
18. **Reports & Analytics Screen** 🆕 (Rapportages)

---

## 🎯 **MOCKUP STRATEGIE**

### **Design Principes:**
- **Consistent Header**: Logo, titel, user menu op alle schermen
- **Sidebar Navigation**: Role-based menu items
- **Color Scheme**: Header #2C3E50, Background #1B3A4B, Cards #A9C6E8, Buttons #F39C12
- **Responsive Layout**: Tablet geoptimaliseerd (1366x768)
- **Accessibility**: Duidelijke labels, contrast, touch-friendly controls

### **Functionaliteit Focus:**
- **Role-based Access**: Verschillende views per gebruikerstype
- **Real-time Data**: Live updates van voorraad, orders, metrics
- **Search & Filter**: Advanced filtering op alle data
- **Mobile-first**: Touch-optimized controls en layouts
- **Integration**: Seamless flow tussen alle schermen

---

## 📱 **SCHERM CATEGORIEËN**

### **🔐 Authentication Schermen:**
- Login Screen (bestaand)
- User Registration Screen (nieuw)
- Forgot Password Screen (nieuw)

### **📊 Management Schermen:**
- Dashboard Screen (update)
- Settings Screen (update)
- Reports & Analytics Screen (nieuw)

### **👥 Customer & Order Schermen:**
- Customer List Screen (update)
- New Order Screen (update)
- Order Detail Screen (update)
- Order Item Screen (update)
- Order History Screen (update)
- Order Confirmation Screen (update)

### **📦 Product & Inventory Schermen:**
- Product Catalog Screen (update)
- Raw Materials Management Screen (nieuw)
- Material Order Screen (nieuw)
- Quality Control Screen (nieuw)

### **🚚 Supplier & Production Schermen:**
- Supplier Management Screen (nieuw)
- Production Planning Screen (nieuw)

---

## 🔧 **TECHNISCHE REQUIREMENTS PER SCHERM**

### **Data Integration:**
- **Real-time Collections**: colCustomers, colProducts, colOrders, colRawMaterials, colSuppliers
- **Calculated Fields**: Stock status, reorder alerts, order totals, material costs
- **Relationships**: Foreign keys tussen customers, orders, products, materials
- **Performance**: Efficient filtering en searching

### **User Experience:**
- **Navigation Flow**: Logical screen transitions
- **Error Handling**: Validation messages, required fields
- **Loading States**: Progress indicators voor data loading
- **Offline Support**: Local storage voor critical data

### **Business Logic:**
- **Role Permissions**: Admin, Manager, Sales, Service access levels
- **Workflow Automation**: Order processing, reorder alerts, status updates
- **Cost Calculations**: Material costs, order profitability, supplier performance
- **Quality Control**: Material inspection, approval workflows

---

## 📋 **MOCKUP CREATION PLAN**

### **Fase 1: Core Schermen (Week 1)**
1. User Registration Screen
2. Forgot Password Screen  
3. Updated Dashboard Screen
4. Updated Customer List Screen
5. Updated Product Catalog Screen

### **Fase 2: Order Management (Week 2)**
6. Updated New Order Screen
7. Updated Order Detail Screen
8. Updated Order Item Screen
9. Updated Order History Screen
10. Updated Order Confirmation Screen

### **Fase 3: Database Integration (Week 3)**
11. Raw Materials Management Screen
12. Supplier Management Screen
13. Material Order Screen
14. Quality Control Screen
15. Production Planning Screen

### **Fase 4: Analytics & Settings (Week 4)**
16. Reports & Analytics Screen
17. Updated Settings Screen
18. Final integration testing
19. User acceptance testing

---

## 🎨 **DESIGN SPECIFICATIONS**

### **Layout Grid:**
- **Header**: 60px height, full width
- **Sidebar**: 200px width, full height minus header
- **Main Content**: Remaining space (1166px width x 708px height)
- **Cards**: 8px border radius, 2px border, consistent spacing
- **Buttons**: 35-45px height, appropriate width, hover effects

### **Typography:**
- **Headers**: Arial Bold, 16-24pt, White (#FFFFFF)
- **Body Text**: Arial Regular, 12-14pt, Dark Blue (#2C3E50)
- **Menu Items**: Arial Regular, 14pt, White (#FFFFFF)
- **Buttons**: Arial Bold, 12-14pt, White (#FFFFFF)

### **Interactive Elements:**
- **Hover Effects**: Color transitions, subtle shadows
- **Active States**: Highlighted backgrounds, border changes
- **Loading States**: Spinners, progress bars, skeleton screens
- **Error States**: Red borders, warning icons, clear messages

### **Data Visualization:**
- **Charts**: Clean, colorful, interactive
- **Tables**: Sortable headers, alternating row colors
- **Cards**: Consistent layout, clear hierarchy
- **Metrics**: Large numbers, trend indicators, color coding

---

## 📄 **DELIVERABLES**

### **Per Scherm:**
1. **Mockup Image**: High-fidelity visual design
2. **Functional Specification**: Detailed requirements
3. **PowerApps Code**: Complete implementation guide
4. **Test Scenarios**: User acceptance criteria

### **Documentation:**
1. **Design System Guide**: Colors, fonts, components
2. **Navigation Flow**: Screen transitions, user journeys
3. **Data Model**: Entity relationships, calculations
4. **Implementation Guide**: Step-by-step PowerApps setup

---

## 🚀 **NEXT STEPS**

1. **Create Individual Mockups**: Start met User Registration Screen
2. **Define Functional Requirements**: Per scherm specificaties
3. **Develop PowerApps Code**: Implementatie per scherm
4. **Test & Iterate**: User feedback en refinements
5. **Deploy & Train**: Production deployment en user training

**Nu ga ik beginnen met het maken van de individuele mockups, startend met de nieuwe schermen die je hebt gespecificeerd.**
