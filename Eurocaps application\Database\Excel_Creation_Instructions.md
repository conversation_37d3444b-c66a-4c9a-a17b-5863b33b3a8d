# Creating the Excel Database File

Follow these steps to create a properly formatted Excel database file from the provided CSV files.

## Step 1: Create a New Excel Workbook

1. Open Microsoft Excel
2. Create a new blank workbook
3. Save it as "EuroCaps_Database.xlsx" in the Database folder

## Step 2: Import the Customers Data

1. Click on Sheet1 (rename it to "Customers")
2. Go to the Data tab in the ribbon
3. Click "From Text/CSV"
4. Browse to the Database folder and select "Customers.csv"
5. In the import dialog, make sure the data looks correct
6. Click "Load"
7. With the data loaded, select all data including headers
8. Go to Insert tab > Table
9. Make sure "My table has headers" is checked
10. Click OK
11. In the Table Design tab that appears, rename the table to "Customers" in the Table Name field

## Step 3: Import the Products Data

1. Create a new sheet (rename it to "Products")
2. Repeat the import process for "Products.csv"
3. Format as a table and name it "Products"

## Step 4: Import the Orders Data

1. Create a new sheet (rename it to "Orders")
2. Repeat the import process for "Orders.csv"
3. Format as a table and name it "Orders"

## Step 5: Import the OrderItems Data

1. Create a new sheet (rename it to "OrderItems")
2. Repeat the import process for "OrderItems.csv"
3. Format as a table and name it "OrderItems"

## Step 6: Format and Finalize

1. For each table, adjust column widths to make all data visible
2. Apply appropriate formatting:
   - Format date columns as dates
   - Format price columns as currency
   - Format ID columns as numbers with no decimals
3. Save the workbook

## Step 7: Prepare for PowerApps

1. Upload the Excel file to OneDrive for Business or SharePoint
2. Make note of the location for use when connecting to PowerApps

## Using the Excel File in PowerApps

When creating your PowerApps application:

1. Add a data connection to Excel Online (Business)
2. Browse to the location where you saved the Excel file
3. Select the file
4. Choose all four tables (Customers, Products, Orders, OrderItems)
5. Click "Connect"

You can now use these tables as data sources in your app, creating galleries, forms, and other controls that connect to this data.
