# ************************************************************************************************
# Enhanced Customer List Screen for EuroCaps Ordering System
# Volledig functioneel customer management met zoeken, filteren en acties
# Gebaseerd op mockup specificaties en database structuur
# ************************************************************************************************

# SCREEN PROPERTIES
Customer_List_Screen As screen:
    Fill: =RGBA(245, 245, 245, 1)  // Background color #f5f5f5
    LoadingSpinnerColor: =RGBA(74, 111, 165, 1)  // Header color #4a6fa5
    
    # OnVisible Event - Initialize customer data and check user permissions
    OnVisible: |
        =// Check if user is logged in
        If(
            IsBlank(varUserRole) || !varIsLoggedIn,
            Navigate(Login_Screen, ScreenTransition.Fade),
            
            // Initialize customer data
            ClearCollect(colAllCustomers,
                {CustomerID: 1, CustomerName: "Coffee World", ContactPerson: "David Lee", Email: "<EMAIL>", Phone: "+31 20 123 4567", Address: "Koffieweg 10, Amsterdam, 1012 AB", CustomerType: "Retail"},
                {CustomerID: 2, CustomerName: "Bean Lovers", ContactPerson: "John Smith", Email: "<EMAIL>", Phone: "+31 30 234 5678", Address: "Bonenstraat 25, Utrecht, 3511 CD", CustomerType: "Wholesale"},
                {CustomerID: 3, CustomerName: "Café Express", ContactPerson: "Maria Garcia", Email: "<EMAIL>", Phone: "+31 10 345 6789", Address: "Espressolaan 5, Rotterdam, 3011 EF", CustomerType: "Retail"},
                {CustomerID: 4, CustomerName: "Morning Brew", ContactPerson: "Sarah Johnson", Email: "<EMAIL>", Phone: "+31 40 456 7890", Address: "Ochtendweg 15, Eindhoven, 5611 GH", CustomerType: "Wholesale"},
                {CustomerID: 5, CustomerName: "The Daily Cup", ContactPerson: "Robert Brown", Email: "<EMAIL>", Phone: "+31 70 567 8901", Address: "Dagelijksestraat 30, Den Haag, 2511 IJ", CustomerType: "Retail"},
                {CustomerID: 6, CustomerName: "Coffee Corner", ContactPerson: "Emma Wilson", Email: "<EMAIL>", Phone: "+31 50 678 9012", Address: "Hoekstraat 8, Groningen, 9711 KL", CustomerType: "Retail"},
                {CustomerID: 7, CustomerName: "Espresso Elite", ContactPerson: "Michael Chen", Email: "<EMAIL>", Phone: "+31 13 789 0123", Address: "Eliteweg 22, Tilburg, 5011 MN", CustomerType: "Wholesale"},
                {CustomerID: 8, CustomerName: "Fresh Grounds", ContactPerson: "Lisa Taylor", Email: "<EMAIL>", Phone: "+31 23 890 1234", Address: "Versstraat 12, Haarlem, 2011 OP", CustomerType: "Retail"},
                {CustomerID: 9, CustomerName: "Java Junction", ContactPerson: "Thomas White", Email: "<EMAIL>", Phone: "+31 76 901 2345", Address: "Kruispunt 5, Breda, 4811 QR", CustomerType: "Wholesale"},
                {CustomerID: 10, CustomerName: "Perfect Pour", ContactPerson: "Amanda Black", Email: "<EMAIL>", Phone: "+31 58 012 3456", Address: "Perfecteweg 18, Leeuwarden, 8911 ST", CustomerType: "Retail"}
            );
            
            // Initialize filtered collection
            ClearCollect(colFilteredCustomers, colAllCustomers);
            
            // Initialize search and filter variables
            Set(varSearchText, "");
            Set(varFilterType, "All");
            Set(varSortBy, "Name");
            Set(varCurrentPage, 1);
            Set(varItemsPerPage, 10);
            Set(varTotalPages, RoundUp(CountRows(colFilteredCustomers) / varItemsPerPage, 0))
        )

# HEADER BAR (REUSED FROM DASHBOARD)
HeaderBar As rectangle:
    Fill: =RGBA(74, 111, 165, 1)  // Header color #4a6fa5
    Height: =60
    Width: =Parent.Width
    X: =0
    Y: =0
    BorderThickness: =0

# HEADER LOGO
HeaderLogo As image:
    Height: =40
    Width: =40
    X: =20
    Y: =10
    Image: ="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSJ3aGl0ZSIgcng9IjQiLz4KPHR5cGUgeD0iMjAiIHk9IjE1IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iOCIgZm9udC13ZWlnaHQ9ImJvbGQiIGZpbGw9IiM0YTZmYTUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPkVDPC90ZXh0Pgo8dGV4dCB4PSIyMCIgeT0iMzAiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSI2IiBmaWxsPSIjNGE2ZmE1IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5PcmRlcjwvdGV4dD4KPC9zdmc+"

# HEADER TITLE
HeaderTitle As label:
    Text: ="EuroCaps Ordering System"
    Font: =Font.Arial
    FontWeight: =FontWeight.Bold
    Size: =16
    Color: =RGBA(255, 255, 255, 1)
    Height: =40
    Width: =300
    X: =70
    Y: =10

# USER DROPDOWN BUTTON
UserDropdown As button:
    Text: =varUserName & " ▼"
    Font: =Font.Arial
    Size: =14
    Color: =RGBA(255, 255, 255, 1)
    Fill: =RGBA(0, 0, 0, 0)
    BorderColor: =RGBA(255, 255, 255, 1)
    BorderThickness: =1
    Height: =40
    Width: =150
    X: =Parent.Width - 270
    Y: =10
    OnSelect: =Set(varShowUserMenu, !varShowUserMenu)

# SETTINGS BUTTON
SettingsButton As button:
    Text: ="⚙ Settings"
    Font: =Font.Arial
    Size: =14
    Color: =RGBA(255, 255, 255, 1)
    Fill: =RGBA(0, 0, 0, 0)
    BorderColor: =RGBA(255, 255, 255, 1)
    BorderThickness: =1
    Height: =40
    Width: =100
    X: =Parent.Width - 110
    Y: =10
    OnSelect: =Navigate(Settings_Screen, ScreenTransition.Fade)

# NAVIGATION MENU SIDEBAR
NavigationMenu As rectangle:
    Fill: =RGBA(58, 90, 128, 1)  // Menu sidebar color #3a5a80
    Height: =Parent.Height - 60
    Width: =200
    X: =0
    Y: =60
    BorderThickness: =0

# MENU DASHBOARD
MenuDashboard As button:
    Text: ="≡ Dashboard"
    Font: =Font.Arial
    Size: =14
    Color: =RGBA(255, 255, 255, 1)
    Fill: =RGBA(58, 90, 128, 1)
    HoverFill: =RGBA(74, 111, 165, 1)
    BorderColor: =RGBA(0, 0, 0, 0)
    Height: =50
    Width: =200
    X: =0
    Y: =60
    Align: =Align.Left
    OnSelect: =Navigate(Dashboard_Screen, ScreenTransition.Fade)

# MENU CUSTOMERS (CURRENT PAGE - HIGHLIGHTED)
MenuCustomers As button:
    Text: ="👥 Customers"
    Font: =Font.Arial
    Size: =14
    Color: =RGBA(255, 255, 255, 1)
    Fill: =RGBA(74, 111, 165, 1)  // Highlighted color
    BorderColor: =RGBA(0, 0, 0, 0)
    Height: =50
    Width: =200
    X: =0
    Y: =110
    Align: =Align.Left
    # Current page - no navigation

# MENU PRODUCTS
MenuProducts As button:
    Text: ="📦 Products"
    Font: =Font.Arial
    Size: =14
    Color: =RGBA(255, 255, 255, 1)
    Fill: =RGBA(58, 90, 128, 1)
    HoverFill: =RGBA(74, 111, 165, 1)
    BorderColor: =RGBA(0, 0, 0, 0)
    Height: =50
    Width: =200
    X: =0
    Y: =160
    Align: =Align.Left
    OnSelect: =Navigate(Product_Catalog_Screen, ScreenTransition.Fade)

# MENU ORDERS
MenuOrders As button:
    Text: ="📋 Orders"
    Font: =Font.Arial
    Size: =14
    Color: =RGBA(255, 255, 255, 1)
    Fill: =RGBA(58, 90, 128, 1)
    HoverFill: =RGBA(74, 111, 165, 1)
    BorderColor: =RGBA(0, 0, 0, 0)
    Height: =50
    Width: =200
    X: =0
    Y: =210
    Align: =Align.Left
    OnSelect: =Navigate(Order_History_Screen, ScreenTransition.Fade)

# MENU LOGOUT
MenuLogout As button:
    Text: ="🚪 Logout"
    Font: =Font.Arial
    Size: =14
    Color: =RGBA(255, 255, 255, 1)
    Fill: =RGBA(58, 90, 128, 1)
    HoverFill: =RGBA(220, 53, 69, 1)
    BorderColor: =RGBA(0, 0, 0, 0)
    Height: =50
    Width: =200
    X: =0
    Y: =Parent.Height - 50
    Align: =Align.Left
    OnSelect: |
        =Set(varUserRole, "");
        Set(varUserName, "");
        Set(varIsLoggedIn, false);
        Notify("Logged out successfully", NotificationType.Success);
        Navigate(Login_Screen, ScreenTransition.Fade)

# PAGE TITLE AND NEW CUSTOMER BUTTON
PageTitle As label:
    Text: ="Customers"
    Font: =Font.Arial
    FontWeight: =FontWeight.Bold
    Size: =24
    Color: =RGBA(51, 51, 51, 1)
    Height: =40
    Width: =200
    X: =220
    Y: =80

NewCustomerButton As button:
    Text: ="+ NEW CUSTOMER"
    Font: =Font.Arial
    FontWeight: =FontWeight.Bold
    Size: =14
    Color: =RGBA(255, 255, 255, 1)
    Fill: =RGBA(243, 156, 18, 1)  // Orange #F39C12
    HoverFill: =RGBA(230, 140, 5, 1)
    BorderColor: =RGBA(243, 156, 18, 1)
    BorderThickness: =0
    BorderRadius: =4
    Height: =40
    Width: =150
    X: =Parent.Width - 170
    Y: =80
    OnSelect: =Navigate(Add_Customer_Screen, ScreenTransition.Fade)

# SEARCH AND FILTER SECTION
# SEARCH INPUT
SearchInput As text input:
    Default: =""
    HintText: ="Search Customers..."
    Font: =Font.Arial
    Size: =12
    Color: =RGBA(51, 51, 51, 1)
    Fill: =RGBA(255, 255, 255, 1)
    BorderColor: =RGBA(204, 204, 204, 1)
    BorderThickness: =1
    BorderRadius: =4
    Height: =35
    Width: =300
    X: =220
    Y: =140
    OnChange: |
        =Set(varSearchText, Self.Text);
        // Apply search filter
        ClearCollect(colFilteredCustomers,
            If(
                IsBlank(varSearchText),
                // No search text - show all customers (filtered by type if applicable)
                If(
                    varFilterType = "All",
                    colAllCustomers,
                    Filter(colAllCustomers, CustomerType = varFilterType)
                ),
                // Search text provided - filter by search and type
                Filter(
                    If(
                        varFilterType = "All",
                        colAllCustomers,
                        Filter(colAllCustomers, CustomerType = varFilterType)
                    ),
                    varSearchText in CustomerName ||
                    varSearchText in ContactPerson ||
                    varSearchText in Email
                )
            )
        );
        // Reset to first page
        Set(varCurrentPage, 1);
        Set(varTotalPages, RoundUp(CountRows(colFilteredCustomers) / varItemsPerPage, 0))

# SEARCH BUTTON
SearchButton As button:
    Text: ="🔍"
    Font: =Font.Arial
    Size: =14
    Color: =RGBA(74, 111, 165, 1)
    Fill: =RGBA(255, 255, 255, 1)
    BorderColor: =RGBA(204, 204, 204, 1)
    BorderThickness: =1
    BorderRadius: =4
    Height: =35
    Width: =40
    X: =530
    Y: =140
    OnSelect: =Select(SearchInput)

# FILTER DROPDOWN
FilterDropdown As dropdown:
    Items: =["All", "Retail", "Wholesale"]
    Default: ="All"
    Font: =Font.Arial
    Size: =12
    Color: =RGBA(51, 51, 51, 1)
    Fill: =RGBA(255, 255, 255, 1)
    BorderColor: =RGBA(204, 204, 204, 1)
    BorderThickness: =1
    BorderRadius: =4
    Height: =35
    Width: =120
    X: =590
    Y: =140
    OnChange: |
        =Set(varFilterType, Self.Selected.Value);
        // Apply filter
        ClearCollect(colFilteredCustomers,
            If(
                IsBlank(varSearchText),
                // No search text
                If(
                    varFilterType = "All",
                    colAllCustomers,
                    Filter(colAllCustomers, CustomerType = varFilterType)
                ),
                // With search text
                Filter(
                    If(
                        varFilterType = "All",
                        colAllCustomers,
                        Filter(colAllCustomers, CustomerType = varFilterType)
                    ),
                    varSearchText in CustomerName ||
                    varSearchText in ContactPerson ||
                    varSearchText in Email
                )
            )
        );
        Set(varCurrentPage, 1);
        Set(varTotalPages, RoundUp(CountRows(colFilteredCustomers) / varItemsPerPage, 0))

# SORT DROPDOWN
SortDropdown As dropdown:
    Items: =["Name", "Contact Person", "Email", "Customer Type"]
    Default: ="Name"
    Font: =Font.Arial
    Size: =12
    Color: =RGBA(51, 51, 51, 1)
    Fill: =RGBA(255, 255, 255, 1)
    BorderColor: =RGBA(204, 204, 204, 1)
    BorderThickness: =1
    BorderRadius: =4
    Height: =35
    Width: =140
    X: =720
    Y: =140
    OnChange: |
        =Set(varSortBy, Self.Selected.Value);
        // Apply sorting
        ClearCollect(colFilteredCustomers,
            Switch(
                varSortBy,
                "Name", SortByColumns(colFilteredCustomers, "CustomerName", Ascending),
                "Contact Person", SortByColumns(colFilteredCustomers, "ContactPerson", Ascending),
                "Email", SortByColumns(colFilteredCustomers, "Email", Ascending),
                "Customer Type", SortByColumns(colFilteredCustomers, "CustomerType", Ascending),
                colFilteredCustomers
            )
        )

# RESET FILTERS BUTTON
ResetFiltersButton As button:
    Text: ="Reset Filters"
    Font: =Font.Arial
    Size: =12
    Color: =RGBA(74, 111, 165, 1)
    Fill: =RGBA(255, 255, 255, 1)
    HoverFill: =RGBA(245, 245, 245, 1)
    BorderColor: =RGBA(74, 111, 165, 1)
    BorderThickness: =1
    BorderRadius: =4
    Height: =35
    Width: =100
    X: =870
    Y: =140
    OnSelect: |
        =// Reset all filters
        Reset(SearchInput);
        Reset(FilterDropdown);
        Reset(SortDropdown);
        Set(varSearchText, "");
        Set(varFilterType, "All");
        Set(varSortBy, "Name");
        ClearCollect(colFilteredCustomers, colAllCustomers);
        Set(varCurrentPage, 1);
        Set(varTotalPages, RoundUp(CountRows(colFilteredCustomers) / varItemsPerPage, 0))

# CUSTOMER TABLE CONTAINER
CustomerTableContainer As rectangle:
    Fill: =RGBA(255, 255, 255, 1)
    Height: =400
    Width: =750
    X: =220
    Y: =200
    BorderRadius: =8
    BorderColor: =RGBA(204, 204, 204, 1)
    BorderThickness: =1

# TABLE HEADERS
NameHeader As label:
    Text: ="Name"
    Font: =Font.Arial
    FontWeight: =FontWeight.Bold
    Size: =12
    Color: =RGBA(51, 51, 51, 1)
    Height: =30
    Width: =150
    X: =230
    Y: =210
    BorderColor: =RGBA(204, 204, 204, 1)
    BorderThickness: =1

ContactHeader As label:
    Text: ="Contact"
    Font: =Font.Arial
    FontWeight: =FontWeight.Bold
    Size: =12
    Color: =RGBA(51, 51, 51, 1)
    Height: =30
    Width: =150
    X: =380
    Y: =210
    BorderColor: =RGBA(204, 204, 204, 1)
    BorderThickness: =1

EmailHeader As label:
    Text: ="Email"
    Font: =Font.Arial
    FontWeight: =FontWeight.Bold
    Size: =12
    Color: =RGBA(51, 51, 51, 1)
    Height: =30
    Width: =200
    X: =530
    Y: =210
    BorderColor: =RGBA(204, 204, 204, 1)
    BorderThickness: =1

ActionsHeader As label:
    Text: ="Actions"
    Font: =Font.Arial
    FontWeight: =FontWeight.Bold
    Size: =12
    Color: =RGBA(51, 51, 51, 1)
    Height: =30
    Width: =120
    X: =730
    Y: =210
    BorderColor: =RGBA(204, 204, 204, 1)
    BorderThickness: =1
    Align: =Align.Center

# CUSTOMER TABLE GALLERY
CustomerGallery As gallery:
    Items: =FirstN(
        LastN(
            colFilteredCustomers,
            CountRows(colFilteredCustomers) - (varCurrentPage - 1) * varItemsPerPage
        ),
        varItemsPerPage
    )
    Height: =350
    Width: =720
    X: =230
    Y: =240
    TemplateSize: =35
    BorderThickness: =0
    Fill: =RGBA(255, 255, 255, 1)

    # Gallery Template
    Template:
        # Customer Name
        CustomerNameLabel As label:
            Text: =ThisItem.CustomerName
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(51, 51, 51, 1)
            Height: =30
            Width: =140
            X: =5
            Y: =2

        # Contact Person
        ContactPersonLabel As label:
            Text: =ThisItem.ContactPerson
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(51, 51, 51, 1)
            Height: =30
            Width: =140
            X: =150
            Y: =2

        # Email (truncated if too long)
        EmailLabel As label:
            Text: =If(Len(ThisItem.Email) > 25, Left(ThisItem.Email, 22) & "...", ThisItem.Email)
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(51, 51, 51, 1)
            Height: =30
            Width: =190
            X: =295
            Y: =2

        # Action Buttons Container
        ActionsContainer As rectangle:
            Fill: =RGBA(0, 0, 0, 0)  // Transparent
            Height: =30
            Width: =110
            X: =490
            Y: =2

        # View Button
        ViewButton As button:
            Text: ="👁️"
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(74, 111, 165, 1)  // Blue
            HoverFill: =RGBA(60, 90, 140, 1)
            BorderColor: =RGBA(74, 111, 165, 1)
            BorderThickness: =0
            BorderRadius: =3
            Height: =25
            Width: =30
            X: =495
            Y: =4
            OnSelect: =Navigate(Customer_Details_Screen, ScreenTransition.Fade, {SelectedCustomerID: ThisItem.CustomerID})

        # Edit Button
        EditButton As button:
            Text: ="📝"
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(255, 152, 0, 1)  // Orange
            HoverFill: =RGBA(230, 130, 0, 1)
            BorderColor: =RGBA(255, 152, 0, 1)
            BorderThickness: =0
            BorderRadius: =3
            Height: =25
            Width: =30
            X: =530
            Y: =4
            OnSelect: =Navigate(Edit_Customer_Screen, ScreenTransition.Fade, {SelectedCustomerID: ThisItem.CustomerID})

        # New Order Button
        NewOrderButton As button:
            Text: ="🛒"
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(76, 175, 80, 1)  // Green
            HoverFill: =RGBA(60, 150, 65, 1)
            BorderColor: =RGBA(76, 175, 80, 1)
            BorderThickness: =0
            BorderRadius: =3
            Height: =25
            Width: =30
            X: =565
            Y: =4
            OnSelect: =Navigate(New_Order_Screen, ScreenTransition.Fade, {PreSelectedCustomerID: ThisItem.CustomerID})

        # Row separator
        RowSeparator As rectangle:
            Fill: =RGBA(240, 240, 240, 1)
            Height: =1
            Width: =700
            X: =10
            Y: =32

# PAGINATION SECTION
PaginationContainer As rectangle:
    Fill: =RGBA(0, 0, 0, 0)  // Transparent
    Height: =50
    Width: =750
    X: =220
    Y: =620

# Previous Button
PreviousButton As button:
    Text: ="◀ Previous"
    Font: =Font.Arial
    Size: =12
    Color: =If(varCurrentPage > 1, RGBA(74, 111, 165, 1), RGBA(150, 150, 150, 1))
    Fill: =RGBA(255, 255, 255, 1)
    HoverFill: =If(varCurrentPage > 1, RGBA(245, 245, 245, 1), RGBA(255, 255, 255, 1))
    BorderColor: =If(varCurrentPage > 1, RGBA(74, 111, 165, 1), RGBA(200, 200, 200, 1))
    BorderThickness: =1
    BorderRadius: =4
    Height: =35
    Width: =100
    X: =220
    Y: =630
    DisplayMode: =If(varCurrentPage > 1, DisplayMode.Edit, DisplayMode.Disabled)
    OnSelect: |
        =If(
            varCurrentPage > 1,
            Set(varCurrentPage, varCurrentPage - 1)
        )

# Page Info
PageInfo As label:
    Text: ="Showing " & Text((varCurrentPage - 1) * varItemsPerPage + 1) & "-" &
           Text(Min(varCurrentPage * varItemsPerPage, CountRows(colFilteredCustomers))) &
           " of " & Text(CountRows(colFilteredCustomers)) & " customers"
    Font: =Font.Arial
    Size: =12
    Color: =RGBA(51, 51, 51, 1)
    Height: =35
    Width: =300
    X: =400
    Y: =630
    Align: =Align.Center

# Next Button
NextButton As button:
    Text: ="Next ▶"
    Font: =Font.Arial
    Size: =12
    Color: =If(varCurrentPage < varTotalPages, RGBA(74, 111, 165, 1), RGBA(150, 150, 150, 1))
    Fill: =RGBA(255, 255, 255, 1)
    HoverFill: =If(varCurrentPage < varTotalPages, RGBA(245, 245, 245, 1), RGBA(255, 255, 255, 1))
    BorderColor: =If(varCurrentPage < varTotalPages, RGBA(74, 111, 165, 1), RGBA(200, 200, 200, 1))
    BorderThickness: =1
    BorderRadius: =4
    Height: =35
    Width: =100
    X: =870
    Y: =630
    DisplayMode: =If(varCurrentPage < varTotalPages, DisplayMode.Edit, DisplayMode.Disabled)
    OnSelect: |
        =If(
            varCurrentPage < varTotalPages,
            Set(varCurrentPage, varCurrentPage + 1)
        )
