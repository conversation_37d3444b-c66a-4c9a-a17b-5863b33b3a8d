# 🔐 FORGOT PASSWORD SCREEN MOCKUP
## Wachtwoord Reset Functionaliteit

---

## 🎨 **VISUAL MOCKUP BESCHRIJVING**

```
┌─────────────────────────────────────────────────────────────────────────────────────────────────────┐
│ [🏢 EuroCaps Logo]  EuroCaps Order Management Pro                           [Help] [Language: EN] │
│                                                                                                     │
└─────────────────────────────────────────────────────────────────────────────────────────────────────┘
│                                                                                                     │
│                                                                                                     │
│                                    ┌─────────────────────────────────┐                            │
│                                    │      🔐 FORGOT PASSWORD?        │                            │
│                                    │                                 │                            │
│                                    │  Don't worry, it happens to     │                            │
│                                    │  the best of us!               │                            │
│                                    └─────────────────────────────────┘                            │
│                                                                                                     │
│                                                                                                     │
│                          ┌─────────────────────────────────────────────────────┐                  │
│                          │              RESET YOUR PASSWORD                    │                  │
│                          │                                                     │                  │
│                          │  Enter your email address and we'll send you       │                  │
│                          │  a link to reset your password.                    │                  │
│                          │                                                     │                  │
│                          │  📧 Email Address:                                  │                  │
│                          │  [________________________________] *Required      │                  │
│                          │                                                     │                  │
│                          │  ┌─────────────────────────────────────────────┐   │                  │
│                          │  │  [🚀 SEND RESET LINK]                      │   │                  │
│                          │  └─────────────────────────────────────────────┘   │                  │
│                          │                                                     │                  │
│                          │  ┌─────────────────────────────────────────────┐   │                  │
│                          │  │  [🔙 BACK TO LOGIN]                        │   │                  │
│                          │  └─────────────────────────────────────────────┘   │                  │
│                          └─────────────────────────────────────────────────────┘                  │
│                                                                                                     │
│                                                                                                     │
│                          ┌─────────────────────────────────────────────────────┐                  │
│                          │                 ALTERNATIVE OPTIONS                 │                  │
│                          │                                                     │                  │
│                          │  🆔 Remember your username but not email?          │                  │
│                          │  [📝 FIND BY USERNAME]                             │                  │
│                          │                                                     │                  │
│                          │  📱 Prefer SMS reset?                              │                  │
│                          │  [📱 SEND SMS CODE]                                │                  │
│                          │                                                     │                  │
│                          │  🔐 Need immediate access?                         │                  │
│                          │  [📞 CONTACT ADMIN]                                │                  │
│                          └─────────────────────────────────────────────────────┘                  │
│                                                                                                     │
│                                                                                                     │
│                          ┌─────────────────────────────────────────────────────┐                  │
│                          │                   HELP & SUPPORT                   │                  │
│                          │                                                     │                  │
│                          │  ❓ Still having trouble?                          │                  │
│                          │                                                     │                  │
│                          │  📧 Email: <EMAIL>                    │                  │
│                          │  📱 Phone: +31 20 123 4567                         │                  │
│                          │  🕒 Hours: Mon-Fri 9:00-17:00 CET                  │                  │
│                          │                                                     │                  │
│                          │  💡 Tips:                                          │                  │
│                          │  • Check your spam folder                          │                  │
│                          │  • Make sure email address is correct              │                  │
│                          │  • Reset link expires in 24 hours                 │                  │
│                          └─────────────────────────────────────────────────────┘                  │
│                                                                                                     │
└─────────────────────────────────────────────────────────────────────────────────────────────────────┘
```

---

## 🔧 **FUNCTIONAL REQUIREMENTS**

### **Primary Reset Flow:**

#### **Email Input Field:**
- **Required**: Yes
- **Validation**: Valid email format
- **Lookup**: Check if email exists in system
- **Real-time Feedback**: Show if email is found/not found
- **Security**: Don't reveal if email exists (for security)

#### **Send Reset Link Button:**
- **Action**: Generate secure reset token
- **Email**: Send reset link to user
- **Expiry**: Link expires in 24 hours
- **Rate Limiting**: Max 3 requests per hour per email
- **Feedback**: Success message regardless of email existence

### **Alternative Reset Options:**

#### **Find by Username:**
- **Input**: Username field
- **Lookup**: Find associated email
- **Privacy**: Show masked email (j***@example.com)
- **Confirmation**: User confirms this is their email
- **Process**: Same as email reset flow

#### **SMS Reset:**
- **Requirement**: Phone number must be on file
- **Process**: Send 6-digit verification code
- **Expiry**: Code expires in 15 minutes
- **Rate Limiting**: Max 5 SMS per day
- **Verification**: User enters code to proceed

#### **Contact Admin:**
- **Direct Link**: Opens email to admin
- **Template**: Pre-filled email with user info
- **Escalation**: For urgent access needs
- **Business Hours**: Show admin availability

---

## 🎯 **USER EXPERIENCE FLOW**

### **Standard Email Reset:**
1. User clicks "Forgot Password" from Login screen
2. User enters email address
3. System validates email format
4. User clicks "Send Reset Link"
5. Success message displayed (regardless of email existence)
6. If email exists, reset link sent
7. User checks email and clicks link
8. Redirected to password reset form
9. User enters new password
10. Password updated, user redirected to login

### **Username Lookup Flow:**
1. User clicks "Find by Username"
2. Modal opens with username field
3. User enters username
4. System shows masked email if found
5. User confirms email
6. Reset link sent to confirmed email
7. Continue with standard flow

### **SMS Reset Flow:**
1. User clicks "Send SMS Code"
2. System checks if phone number on file
3. 6-digit code sent via SMS
4. User enters verification code
5. Code validated
6. User redirected to password reset form
7. New password set

### **Admin Contact Flow:**
1. User clicks "Contact Admin"
2. Email client opens with pre-filled template
3. User adds details about access issue
4. Admin receives request
5. Admin manually resets password or provides access

---

## 🔒 **SECURITY FEATURES**

### **Token Security:**
- **Unique Tokens**: Cryptographically secure random tokens
- **Single Use**: Token invalidated after use
- **Expiration**: 24-hour expiry for email, 15-minute for SMS
- **Secure Storage**: Tokens hashed in database

### **Rate Limiting:**
- **Email Requests**: Max 3 per hour per email
- **SMS Requests**: Max 5 per day per phone
- **IP Limiting**: Max 10 requests per hour per IP
- **Account Lockout**: Temporary lockout after excessive attempts

### **Privacy Protection:**
- **Email Enumeration**: Don't reveal if email exists
- **Consistent Responses**: Same message for valid/invalid emails
- **Masked Information**: Show partial email/phone only
- **Audit Logging**: Log all reset attempts

### **Validation:**
- **Email Format**: RFC compliant email validation
- **Phone Format**: International phone number validation
- **Username Format**: Alphanumeric validation
- **Input Sanitization**: Prevent injection attacks

---

## 📱 **RESPONSIVE DESIGN**

### **Desktop Layout:**
- **Centered Form**: 500px width, centered on screen
- **Clear Hierarchy**: Large headings, readable text
- **Adequate Spacing**: Comfortable padding and margins

### **Tablet Layout:**
- **Full Width**: Form takes most of screen width
- **Touch Targets**: Larger buttons for touch interaction
- **Readable Text**: Appropriate font sizes

### **Mobile Layout:**
- **Single Column**: Stacked elements
- **Thumb Navigation**: Easy reach for all controls
- **Minimal Scrolling**: Compact but readable design

---

## 🎨 **STYLING SPECIFICATIONS**

### **Colors:**
- **Background**: #1B3A4B (Dark blue-gray)
- **Form Background**: #A9C6E8 (Light blue)
- **Primary Button**: #F39C12 (Orange)
- **Secondary Button**: #6C757D (Gray)
- **Success Message**: #28A745 (Green)
- **Error Message**: #DC3545 (Red)
- **Info Message**: #17A2B8 (Blue)

### **Typography:**
- **Main Heading**: Arial Bold, 20pt, #2C3E50
- **Subheading**: Arial Regular, 16pt, #2C3E50
- **Body Text**: Arial Regular, 14pt, #2C3E50
- **Help Text**: Arial Regular, 12pt, #6C757D
- **Button Text**: Arial Bold, 14pt, #FFFFFF

### **Interactive Elements:**
- **Input Focus**: Orange border (#F39C12)
- **Button Hover**: Darker shade of button color
- **Loading State**: Spinner with orange color
- **Success State**: Green checkmark icon
- **Error State**: Red warning icon

---

## 📧 **EMAIL TEMPLATE**

### **Reset Email Content:**
```
Subject: Reset Your EuroCaps Password

Dear [Username],

You recently requested to reset your password for your EuroCaps account.

Click the link below to reset your password:
[RESET PASSWORD BUTTON]

This link will expire in 24 hours for security reasons.

If you didn't request this reset, please ignore this email or contact support.

Best regards,
EuroCaps Support Team

---
Need help? Contact us:
📧 <EMAIL>
📱 +31 20 123 4567
```

---

## 🧪 **TESTING SCENARIOS**

### **Positive Tests:**
1. **Valid Email**: Reset link sent successfully
2. **Username Lookup**: Correct email found and masked
3. **SMS Code**: Code sent and verified successfully
4. **Link Click**: Reset form opens correctly
5. **Password Update**: New password saved successfully

### **Negative Tests:**
1. **Invalid Email**: Proper error message shown
2. **Non-existent Email**: Same success message (security)
3. **Expired Link**: Clear error message
4. **Used Link**: Link already used error
5. **Rate Limiting**: Too many requests blocked

### **Edge Cases:**
1. **Network Issues**: Graceful error handling
2. **Email Delivery**: Retry mechanism for failed emails
3. **SMS Delivery**: Fallback to email if SMS fails
4. **Browser Compatibility**: Works across browsers
5. **Accessibility**: Screen reader compatible

**Deze mockup vormt de basis voor de Forgot Password Screen implementatie in PowerApps.**
