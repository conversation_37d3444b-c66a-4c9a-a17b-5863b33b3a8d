# Dashboard Screen - EuroCaps Ordering System

## Screen Layout

```
+---------------------------------------------------------------+
| [Logo] EuroCaps Ordering System           [User ▼] [Settings] |
+---------------------------------------------------------------+
| [≡ MENU]  | Dashboard                                         |
|           |                                                    |
| Dashboard | +-------------------+  +-------------------+       |
| Customers | | NEW ORDERS        |  | PROCESSING ORDERS |       |
| Products  | | 12                |  | 8                 |       |
| Orders    | +-------------------+  +-------------------+       |
| Reports   |                                                    |
|           | +-------------------+  +-------------------+       |
| Settings  | | SHIPPED ORDERS    |  | DELIVERED ORDERS  |       |
| Logout    | | 15                |  | 42                |       |
|           | +-------------------+  +-------------------+       |
|           |                                                    |
|           | RECENT ORDERS                                      |
|           | +------------------------------------------------+ |
|           | | Order #  | Customer      | Date       | Status | |
|           | |-------------------------------------------------| |
|           | | ORD-1089 | Coffee World  | 15/05/2025 | New    | |
|           | | ORD-1088 | Bean Lovers   | 14/05/2025 | New    | |
|           | | ORD-1087 | Café Express  | 14/05/2025 | Process| |
|           | | ORD-1086 | Morning Brew  | 13/05/2025 | Shipped| |
|           | | ORD-1085 | Coffee World  | 12/05/2025 | Process| |
|           | +------------------------------------------------+ |
|           |                                                    |
|           | QUICK ACTIONS                                      |
|           | [+ NEW ORDER]  [VIEW CUSTOMERS]  [VIEW PRODUCTS]   |
|           |                                                    |
+---------------------------------------------------------------+
```

## Design Elements

### Colors
- Header: Blue (#4a6fa5)
- Menu sidebar: Dark blue (#3a5a80)
- Background: Light gray (#f5f5f5)
- Cards: White (#ffffff)
- Status indicators:
  - New: Orange (#ff9800)
  - Processing: Blue (#4a6fa5)
  - Shipped: Purple (#9c27b0)
  - Delivered: Green (#4caf50)

### Typography
- Header: Arial, 16pt, Bold, White
- Menu items: Arial, 14pt, White
- Card titles: Arial, 14pt, Bold, Dark gray
- Card values: Arial, 24pt, Bold, Status color
- Table headers: Arial, 12pt, Bold
- Table content: Arial, 12pt
- Button text: Arial, 14pt, Bold, White

### Components

1. **Header Bar**
   - EuroCaps logo (left-aligned)
   - Application title
   - User profile dropdown (right-aligned)
   - Settings icon (right-aligned)

2. **Navigation Menu**
   - Vertical sidebar with menu items
   - Current page highlighted
   - Icons for each menu item

3. **Status Cards**
   - Four cards showing order counts by status
   - Each with distinct color coding
   - Large numbers for quick visibility
   - Clickable to filter order list by status

4. **Recent Orders Table**
   - Sortable columns
   - Status indicated by color
   - Clickable rows to view order details
   - Limited to 5 most recent orders

5. **Quick Actions**
   - Button group for common tasks
   - Prominent "New Order" button
   - Secondary action buttons

## Interactions

1. **Menu Navigation**
   - Click on menu items to navigate to different screens
   - Current screen highlighted in menu

2. **Status Cards**
   - Click on card to view filtered list of orders by that status

3. **Recent Orders**
   - Click on order row to navigate to Order Details screen
   - Hover effect on rows
   - Sort by clicking column headers

4. **Quick Actions**
   - "New Order" button navigates to New Order screen
   - "View Customers" navigates to Customer List screen
   - "View Products" navigates to Product Catalog screen

5. **User Menu**
   - Click on username to show dropdown
   - Options: Profile, Preferences, Logout

## Accessibility Considerations
- Clear visual hierarchy
- Consistent navigation patterns
- Color is not the only indicator of status (text labels included)
- Sufficient contrast for all text elements

## Notes for Implementation
- Dashboard should refresh automatically every few minutes
- Consider adding notifications area for system messages
- For prototype: Use mock data for all metrics and tables
