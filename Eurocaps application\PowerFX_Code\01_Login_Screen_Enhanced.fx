# ************************************************************************************************
# Enhanced Login Screen for EuroCaps Ordering System
# Volledig functioneel login scherm met stakeholder-gebaseerde authenticatie
# Gebaseerd op mockup specificaties en use case diagrammen
# ************************************************************************************************

# SCREEN PROPERTIES
Login_Screen As screen:
    Fill: =RGBA(27, 58, 75, 1)  // Background color #1B3A4B
    LoadingSpinnerColor: =RGBA(243, 156, 18, 1)  // Button color #F39C12
    
    # OnVisible Event - Initialize login variables and check remembered credentials
    OnVisible: |
        =// Initialize login variables
        Set(varUserRole, "");
        Set(varUserName, "");
        Set(varIsLoggedIn, false);
        Set(varRememberMe, false);
        Set(varLoginAttempts, 0);
        
        // Check if user credentials are remembered
        If(
            !IsBlank(Get("RememberedUser")),
            Set(varRememberMe, true);
            Set(UsernameInput.Text, Get("RememberedUser"));
            Set(RememberMeCheckbox.Value, true)
        );
        
        // Initialize stakeholder data for authentication
        ClearCollect(colStakeholders,
            {StakeholderID: 1, Username: "admin", Password: "admin123", Role: "Administrator", Name: "System Administrator", AccessLevel: "Full"},
            {StakeholderID: 2, Username: "manager", Password: "manager123", Role: "Manager", Name: "Operations Manager", AccessLevel: "Management"},
            {StakeholderID: 3, Username: "sales", Password: "sales123", Role: "Sales", Name: "Sales Representative", AccessLevel: "Sales"},
            {StakeholderID: 4, Username: "customer", Password: "customer123", Role: "Customer", Name: "Customer User", AccessLevel: "Limited"},
            {StakeholderID: 5, Username: "supplier", Password: "supplier123", Role: "Supplier", Name: "Supplier Contact", AccessLevel: "Supplier"}
        )

# BACKGROUND CONTAINER
Background As rectangle:
    Fill: =RGBA(27, 58, 75, 1)  // Background color #1B3A4B
    Height: =Parent.Height
    Width: =Parent.Width
    X: =0
    Y: =0

# EUROCAPS LOGO
Logo As image:
    Height: =150
    Width: =150
    X: =(Parent.Width - Self.Width) / 2
    Y: =80
    # SVG Logo data voor EuroCaps
    Image: ="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTUwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDE1MCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxNTAiIGhlaWdodD0iMTUwIiBmaWxsPSIjRjM5QzEyIiByeD0iMTAiLz4KPHR5cGUgeD0iNzUiIHk9IjYwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMjQiIGZvbnQtd2VpZ2h0PSJib2xkIiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+RXVyb0NhcHM8L3RleHQ+Cjx0ZXh0IHg9Ijc1IiB5PSI5MCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+T3JkZXJpbmcgU3lzdGVtPC90ZXh0Pgo8L3N2Zz4="

# APPLICATION TITLE
AppTitle As label:
    Text: ="Coffee Capsule Ordering System"
    Font: =Font.Arial
    FontWeight: =FontWeight.Bold
    Size: =18
    Color: =RGBA(255, 255, 255, 1)  // Text color #FFFFFF
    Height: =40
    Width: =400
    X: =(Parent.Width - Self.Width) / 2
    Y: =250
    Align: =Align.Center

# LOGIN PANEL CONTAINER
LoginPanel As rectangle:
    Fill: =RGBA(255, 255, 255, 1)  // White background for login panel
    Height: =320
    Width: =400
    X: =(Parent.Width - Self.Width) / 2
    Y: =300
    BorderRadius: =8
    BorderThickness: =0
    # Shadow effect simulation
    DropShadow: =DropShadow.Light

# USERNAME LABEL
UsernameLabel As label:
    Text: ="Username:"
    Font: =Font.Arial
    FontWeight: =FontWeight.Bold
    Size: =12
    Color: =RGBA(51, 51, 51, 1)  // Dark gray
    Height: =25
    Width: =100
    X: =LoginPanel.X + 20
    Y: =LoginPanel.Y + 30

# USERNAME INPUT FIELD
UsernameInput As text input:
    Default: =""
    Font: =Font.Arial
    Size: =12
    Color: =RGBA(51, 51, 51, 1)
    Fill: =RGBA(255, 255, 255, 1)
    BorderColor: =RGBA(204, 204, 204, 1)
    BorderThickness: =1
    Height: =35
    Width: =360
    X: =LoginPanel.X + 20
    Y: =LoginPanel.Y + 55
    HintText: ="Enter your username"
    # Validation styling
    BorderColor: =If(IsBlank(Self.Text) && varLoginAttempts > 0, RGBA(255, 0, 0, 1), RGBA(204, 204, 204, 1))

# PASSWORD LABEL
PasswordLabel As label:
    Text: ="Password:"
    Font: =Font.Arial
    FontWeight: =FontWeight.Bold
    Size: =12
    Color: =RGBA(51, 51, 51, 1)
    Height: =25
    Width: =100
    X: =LoginPanel.X + 20
    Y: =LoginPanel.Y + 100

# PASSWORD INPUT FIELD
PasswordInput As text input:
    Default: =""
    Mode: =TextMode.Password
    Font: =Font.Arial
    Size: =12
    Color: =RGBA(51, 51, 51, 1)
    Fill: =RGBA(255, 255, 255, 1)
    BorderColor: =RGBA(204, 204, 204, 1)
    BorderThickness: =1
    Height: =35
    Width: =360
    X: =LoginPanel.X + 20
    Y: =LoginPanel.Y + 125
    HintText: ="Enter your password"
    # Validation styling
    BorderColor: =If(IsBlank(Self.Text) && varLoginAttempts > 0, RGBA(255, 0, 0, 1), RGBA(204, 204, 204, 1))

# REMEMBER ME CHECKBOX
RememberMeCheckbox As checkbox:
    Default: =false
    Text: ="Remember me"
    Font: =Font.Arial
    Size: =12
    Color: =RGBA(51, 51, 51, 1)
    Height: =25
    Width: =150
    X: =LoginPanel.X + 20
    Y: =LoginPanel.Y + 175

# FORGOT PASSWORD LINK
ForgotPasswordLink As button:
    Text: ="Forgot Password?"
    Font: =Font.Arial
    Size: =12
    Color: =RGBA(74, 111, 165, 1)  // Blue link color
    Fill: =RGBA(0, 0, 0, 0)  // Transparent background
    BorderColor: =RGBA(0, 0, 0, 0)  // No border
    Height: =25
    Width: =120
    X: =LoginPanel.X + 260
    Y: =LoginPanel.Y + 175
    OnSelect: =Navigate(Settings_Screen, ScreenTransition.Fade)  // Navigate to settings for password reset

# LOGIN BUTTON
LoginButton As button:
    Text: ="LOGIN"
    Font: =Font.Arial
    FontWeight: =FontWeight.Bold
    Size: =14
    Color: =RGBA(255, 255, 255, 1)  // White text
    Fill: =RGBA(243, 156, 18, 1)  // Button color #F39C12
    HoverFill: =RGBA(230, 140, 5, 1)  // Darker orange on hover
    PressedFill: =RGBA(200, 120, 0, 1)  // Even darker when pressed
    BorderColor: =RGBA(243, 156, 18, 1)
    BorderThickness: =0
    BorderRadius: =4
    Height: =45
    Width: =360
    X: =LoginPanel.X + 20
    Y: =LoginPanel.Y + 220
    
    # LOGIN LOGIC WITH STAKEHOLDER AUTHENTICATION
    OnSelect: |
        =// Increment login attempts
        Set(varLoginAttempts, varLoginAttempts + 1);
        
        // Validate input fields
        If(
            IsBlank(UsernameInput.Text) || IsBlank(PasswordInput.Text),
            Notify("Please enter both username and password", NotificationType.Error),
            
            // Check credentials against stakeholder database
            With(
                LookUp(colStakeholders, Username = UsernameInput.Text && Password = PasswordInput.Text),
                If(
                    IsBlank(ThisRecord),
                    // Invalid credentials
                    Notify("Invalid username or password", NotificationType.Error);
                    Set(varLoginAttempts, varLoginAttempts + 1),
                    
                    // Valid credentials - set user variables
                    Set(varUserName, ThisRecord.Name);
                    Set(varUserRole, ThisRecord.Role);
                    Set(varAccessLevel, ThisRecord.AccessLevel);
                    Set(varIsLoggedIn, true);
                    
                    // Handle Remember Me functionality
                    If(
                        RememberMeCheckbox.Value,
                        Set("RememberedUser", UsernameInput.Text),
                        Remove("RememberedUser")
                    );
                    
                    // Show success message and navigate to dashboard
                    Notify("Login successful! Welcome " & varUserName, NotificationType.Success);
                    Navigate(Dashboard_Screen, ScreenTransition.Fade)
                )
            )
        )

# ERROR MESSAGE DISPLAY
ErrorMessage As label:
    Text: =If(varLoginAttempts > 0 && (IsBlank(UsernameInput.Text) || IsBlank(PasswordInput.Text)), "Please fill in all required fields", "")
    Font: =Font.Arial
    Size: =10
    Color: =RGBA(255, 0, 0, 1)  // Red error text
    Height: =20
    Width: =360
    X: =LoginPanel.X + 20
    Y: =LoginPanel.Y + 275
    Visible: =!IsBlank(Self.Text)

# COPYRIGHT FOOTER
CopyrightFooter As label:
    Text: ="© 2025 EuroCaps - Coffee Capsule Ordering System v1.0"
    Font: =Font.Arial
    Size: =10
    Color: =RGBA(255, 255, 255, 0.7)  // Semi-transparent white
    Height: =20
    Width: =400
    X: =(Parent.Width - Self.Width) / 2
    Y: =Parent.Height - 40
    Align: =Align.Center
