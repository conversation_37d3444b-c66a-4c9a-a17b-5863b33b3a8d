# Settings Screen - EuroCaps Ordering System

## Screen Layout

```
+---------------------------------------------------------------+
| [Logo] EuroCaps Ordering System           [User ▼] [Settings] |
+---------------------------------------------------------------+
| [≡ MENU]  | Settings                                          |
|           |                                                    |
| Dashboard | USER PROFILE                                       |
| Customers | +------------------------------------------------+ |
| Products  | | Name: John <PERSON>                                 | |
| Orders    | | Email: <EMAIL>                   | |
| Reports   | | Role: Sales Representative                     | |
|           | | Last Login: 16/05/2025 09:15                   | |
| Settings  | | [Change Password]                              | |
| Logout    | +------------------------------------------------+ |
|           |                                                    |
|           | NOTIFICATION PREFERENCES                           |
|           | +------------------------------------------------+ |
|           | | [✓] Email notifications for new orders         | |
|           | | [✓] Email notifications for order status changes| |
|           | | [✓] In-app notifications                       | |
|           | | [ ] Daily order summary email                  | |
|           | | [ ] Weekly sales report email                  | |
|           | +------------------------------------------------+ |
|           |                                                    |
|           | DISPLAY PREFERENCES                                |
|           | +------------------------------------------------+ |
|           | | Default view: [Dashboard ▼]                    | |
|           | | Items per page: [10 ▼]                         | |
|           | | Date format: [DD/MM/YYYY ▼]                    | |
|           | | Theme: [Light ▼]                               | |
|           | +------------------------------------------------+ |
|           |                                                    |
|           | DEFAULT VALUES                                     |
|           | +------------------------------------------------+ |
|           | | Default delivery days: [7]                     | |
|           | | Default customer: [None ▼]                     | |
|           | +------------------------------------------------+ |
|           |                                                    |
|           | [SAVE SETTINGS]  [RESET TO DEFAULTS]              |
|           |                                                    |
+---------------------------------------------------------------+
```

## Design Elements

### Colors
- Header: Blue (#4a6fa5)
- Menu sidebar: Dark blue (#3a5a80)
- Background: Light gray (#f5f5f5)
- Settings sections: White (#ffffff)
- Save button: Green (#4caf50)
- Reset button: Gray (#9e9e9e)
- Change Password button: Blue (#4a6fa5)

### Typography
- Header: Arial, 16pt, Bold, White
- Menu items: Arial, 14pt, White
- Page title: Arial, 18pt, Bold, Dark gray
- Section titles: Arial, 16pt, Bold, Dark gray
- Setting labels: Arial, 12pt, Bold
- Setting values: Arial, 12pt
- Button text: Arial, 14pt, Bold, White

### Components

1. **Header Bar**
   - EuroCaps logo (left-aligned)
   - Application title
   - User profile dropdown (right-aligned)
   - Settings icon (right-aligned, highlighted)

2. **Navigation Menu**
   - Vertical sidebar with menu items
   - "Settings" highlighted
   - Icons for each menu item

3. **User Profile Section**
   - Read-only user information
   - Change Password button

4. **Notification Preferences Section**
   - Checkboxes for different notification types
   - Email and in-app notification options

5. **Display Preferences Section**
   - Default view dropdown
   - Items per page dropdown
   - Date format dropdown
   - Theme selector

6. **Default Values Section**
   - Default delivery days input
   - Default customer dropdown

7. **Action Buttons**
   - "Save Settings" (primary)
   - "Reset to Defaults" (secondary)

## Interactions

1. **User Profile**
   - "Change Password" opens password change dialog
   - Profile information is read-only

2. **Notification Preferences**
   - Checkboxes toggle notification settings
   - Changes take effect after saving

3. **Display Preferences**
   - Dropdowns for selecting preferences
   - Theme change may apply immediately for preview

4. **Default Values**
   - Numeric input for delivery days
   - Dropdown for selecting default customer

5. **Form Actions**
   - "Save Settings" validates and saves all settings
   - "Reset to Defaults" prompts for confirmation, then resets all settings to system defaults

## Validation Rules

1. **Numeric Inputs**
   - Default delivery days must be positive integer
   - Items per page must be within allowed range (5-50)

2. **Required Fields**
   - Date format is required
   - Theme is required

## Success Feedback

After successful save:
- Success message appears
- Settings are immediately applied where applicable

## Accessibility Considerations
- Clear visual hierarchy
- Sufficient contrast for all text elements
- Logical grouping of related settings
- Clear labeling for all form controls

## Notes for Implementation
- Consider adding profile picture upload
- Add language preference option
- For prototype: Implement basic settings storage
- Add tooltips for settings explanations
- Consider adding advanced settings section for admin users
