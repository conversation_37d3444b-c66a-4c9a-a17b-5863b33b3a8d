# 🔐 FORGOT PASSWORD SCREEN - CODE IMPLEMENTATIE
## Complete Wachtwoord Reset Functionaliteit

---

## 🎯 **FUNCTIONALITEIT OVERZICHT**

### **Password Reset Features:**
- ✅ Email-based password reset
- ✅ Username lookup functionaliteit
- ✅ SMS verification code option
- ✅ Admin contact voor urgent access
- ✅ Security features en rate limiting
- ✅ Multi-method reset options

---

## 🔧 **STAP-VOOR-STAP CODE IMPLEMENTATIE**

### **STAP 1: NIEUWE SCREEN MAKEN**

#### **Screen Setup:**
1. **Insert → New screen → Blank**
2. **Hernoem naar:** `Forgot_Password_Screen`

#### **Screen Eigenschappen:**
**Waar:** Forgot Password Screen → Fill eigenschap
```powerapps
RGBA(27, 58, 75, 1)
```

**Waar:** Forgot Password Screen → OnVisible eigenschap
```powerapps
// Initialize forgot password variables
Set(varResetInProgress, false);
Set(varResetMethod, "email"); // email, username, sms, admin
Set(varResetMessage, "");
Set(varResetSuccess, false);
Set(varEmailFound, false);
Set(varSMSCodeSent, false);
Set(varResetAttempts, 0);

// Clear input fields
Reset(EmailResetInput);
Reset(UsernameResetInput);
Reset(SMSCodeInput);

// Check rate limiting
Set(varResetLimitReached, 
    !IsBlank(Get("ResetLimit_" & User().Email)) &&
    DateDiff(DateTimeValue(Get("ResetLimit_" & User().Email)), Now(), Hours) < 1)
```

---

### **STAP 2: HEADER EN NAVIGATION**

#### **1. Header Section**
**Toevoegen:** Insert → Icons → Rectangle (Background)
**Eigenschappen:**
- **Fill eigenschap:** `RGBA(44, 62, 80, 1)`
- **Height eigenschap:** `60`
- **Width eigenschap:** `1366`
- **X eigenschap:** `0`
- **Y eigenschap:** `0`

**Toevoegen:** Insert → Text → Label (Title)
**Eigenschappen:**
- **Text eigenschap:** `"Reset Your Password"`
- **Color eigenschap:** `RGBA(255, 255, 255, 1)`
- **FontWeight eigenschap:** `FontWeight.Bold`
- **Size eigenschap:** `20`
- **Height eigenschap:** `40`
- **Width eigenschap:** `300`
- **X eigenschap:** `533`
- **Y eigenschap:** `10`

#### **2. Main Container**
**Toevoegen:** Insert → Icons → Rectangle
**Eigenschappen:**
- **Fill eigenschap:** `RGBA(169, 198, 232, 1)`
- **BorderColor eigenschap:** `RGBA(243, 156, 18, 1)`
- **BorderThickness eigenschap:** `2`
- **Height eigenschap:** `500`
- **Width eigenschap:** `600`
- **X eigenschap:** `383`
- **Y eigenschap:** `100`

---

### **STAP 3: EMAIL RESET SECTION (DEFAULT)**

#### **3. Reset Instructions**
**Toevoegen:** Insert → Text → Label
**Eigenschappen:**
- **Text eigenschap:**
```powerapps
"Enter your email address and we'll send you a link to reset your password."
```
- **Color eigenschap:** `RGBA(44, 62, 80, 1)`
- **Size eigenschap:** `14`
- **Height eigenschap:** `40`
- **Width eigenschap:** `500`
- **X eigenschap:** `433`
- **Y eigenschap:** `140`

#### **4. Email Input**
**Toevoegen:** Insert → Input → Text input
**Naam:** `EmailResetInput`
**Eigenschappen:**
- **Format eigenschap:** `TextFormat.Email`
- **HintText eigenschap:** `"Enter your email address"`
- **BorderColor eigenschap:**
```powerapps
If(
    varResetLimitReached,
    RGBA(220, 53, 69, 1),  // Red if rate limited
    If(
        !IsBlank(varResetMessage) && !varResetSuccess,
        RGBA(220, 53, 69, 1),  // Red on error
        RGBA(243, 156, 18, 1)  // Orange default
    )
)
```
- **Height eigenschap:** `40`
- **Width eigenschap:** `400`
- **X eigenschap:** `483`
- **Y eigenschap:** `190`
- **Visible eigenschap:**
```powerapps
varResetMethod = "email"
```

#### **5. Send Reset Link Button**
**Toevoegen:** Insert → Input → Button
**Eigenschappen:**
- **Text eigenschap:** `"🚀 SEND RESET LINK"`
- **Fill eigenschap:**
```powerapps
If(
    varResetInProgress,
    RGBA(108, 117, 125, 1),  // Gray when loading
    If(
        varResetLimitReached,
        RGBA(220, 53, 69, 1),  // Red when rate limited
        RGBA(243, 156, 18, 1)  // Orange when ready
    )
)
```
- **Color eigenschap:** `RGBA(255, 255, 255, 1)`
- **FontWeight eigenschap:** `FontWeight.Bold`
- **Height eigenschap:** `45`
- **Width eigenschap:** `200`
- **X eigenschap:** `583`
- **Y eigenschap:** `250`
- **DisplayMode eigenschap:**
```powerapps
If(
    varResetInProgress || varResetLimitReached || IsBlank(EmailResetInput.Text),
    DisplayMode.Disabled,
    DisplayMode.Edit
)
```
- **Visible eigenschap:**
```powerapps
varResetMethod = "email"
```
- **OnSelect eigenschap:**
```powerapps
// Check rate limiting
If(
    varResetLimitReached,
    Set(varResetMessage, "Too many reset attempts. Please wait 1 hour before trying again."),
    
    // Start reset process
    Set(varResetInProgress, true);
    Set(varResetMessage, "");
    
    // Simulate email lookup (in real app, check database)
    Set(varEmailFound, 
        EmailResetInput.Text in ["<EMAIL>", "<EMAIL>", "<EMAIL>"]);
    
    // Always show success message for security (don't reveal if email exists)
    Set(varResetInProgress, false);
    Set(varResetSuccess, true);
    Set(varResetMessage, "If this email address is in our system, you will receive a password reset link shortly.");
    
    // Set rate limiting
    Set("ResetLimit_" & EmailResetInput.Text, Text(Now()));
    Set(varResetAttempts, varResetAttempts + 1);
    
    // If email found, simulate sending email (in real app, send actual email)
    If(varEmailFound,
        Notify("Password reset email sent successfully", NotificationType.Success),
        // Don't notify if email not found for security
        ""
    )
)
```

---

### **STAP 4: ALTERNATIVE RESET OPTIONS**

#### **6. Alternative Options Container**
**Toevoegen:** Insert → Icons → Rectangle
**Eigenschappen:**
- **Fill eigenschap:** `RGBA(169, 198, 232, 0.5)`
- **BorderColor eigenschap:** `RGBA(243, 156, 18, 1)`
- **BorderThickness eigenschap:** `1`
- **Height eigenschap:** `150`
- **Width eigenschap:** `500`
- **X eigenschap:** `433`
- **Y eigenschap:** `320`

#### **7. Username Lookup Button**
**Toevoegen:** Insert → Input → Button
**Eigenschappen:**
- **Text eigenschap:** `"📝 FIND BY USERNAME"`
- **Fill eigenschap:** `RGBA(108, 117, 125, 1)`
- **Color eigenschap:** `RGBA(255, 255, 255, 1)`
- **Height eigenschap:** `35`
- **Width eigenschap:** `150`
- **X eigenschap:** `453`
- **Y eigenschap:** `340`
- **OnSelect eigenschap:**
```powerapps
Set(varResetMethod, "username");
Reset(UsernameResetInput)
```

#### **8. SMS Reset Button**
**Toevoegen:** Insert → Input → Button
**Eigenschappen:**
- **Text eigenschap:** `"📱 SEND SMS CODE"`
- **Fill eigenschap:** `RGBA(108, 117, 125, 1)`
- **Color eigenschap:** `RGBA(255, 255, 255, 1)`
- **Height eigenschap:** `35`
- **Width eigenschap:** `150`
- **X eigenschap:** `623`
- **Y eigenschap:** `340`
- **OnSelect eigenschap:**
```powerapps
Set(varResetMethod, "sms");
Reset(SMSCodeInput)
```

#### **9. Contact Admin Button**
**Toevoegen:** Insert → Input → Button
**Eigenschappen:**
- **Text eigenschap:** `"📞 CONTACT ADMIN"`
- **Fill eigenschap:** `RGBA(108, 117, 125, 1)`
- **Color eigenschap:** `RGBA(255, 255, 255, 1)`
- **Height eigenschap:** `35`
- **Width eigenschap:** `150`
- **X eigenschap:** `793`
- **Y eigenschap:** `340`
- **OnSelect eigenschap:**
```powerapps
// Open email client with pre-filled admin email
Launch("mailto:<EMAIL>?subject=Password Reset Request&body=Hello Admin,%0D%0A%0D%0AI need help resetting my password for the EuroCaps system.%0D%0A%0D%0AUsername: [Enter your username]%0D%0AEmail: [Enter your email]%0D%0AReason: [Explain why you need immediate access]%0D%0A%0D%0AThank you for your assistance.");

Notify("Email client opened. Please send the email to admin for assistance.", NotificationType.Information)
```

---

### **STAP 5: USERNAME LOOKUP SECTION**

#### **10. Username Input**
**Toevoegen:** Insert → Input → Text input
**Naam:** `UsernameResetInput`
**Eigenschappen:**
- **HintText eigenschap:** `"Enter your username"`
- **Height eigenschap:** `40`
- **Width eigenschap:** `300`
- **X eigenschap:** `533`
- **Y eigenschap:** `190`
- **Visible eigenschap:**
```powerapps
varResetMethod = "username"
```

#### **11. Find Email Button**
**Toevoegen:** Insert → Input → Button
**Eigenschappen:**
- **Text eigenschap:** `"🔍 FIND EMAIL"`
- **Fill eigenschap:** `RGBA(243, 156, 18, 1)`
- **Color eigenschap:** `RGBA(255, 255, 255, 1)`
- **Height eigenschap:** `40`
- **Width eigenschap:** `120`
- **X eigenschap:** `853`
- **Y eigenschap:** `190`
- **Visible eigenschap:**
```powerapps
varResetMethod = "username"
```
- **OnSelect eigenschap:**
```powerapps
// Simulate username lookup
Set(varFoundEmail, 
    Switch(
        Lower(UsernameResetInput.Text),
        "admin", "a***@eurocaps.com",
        "manager", "m***@eurocaps.com", 
        "sales", "s***@eurocaps.com",
        ""
    ));

If(
    !IsBlank(varFoundEmail),
    Set(varResetMessage, "Email found: " & varFoundEmail & ". Click 'Send Reset Link' to continue."),
    Set(varResetMessage, "Username not found. Please check your username or contact support.")
)
```

---

### **STAP 6: SMS RESET SECTION**

#### **12. SMS Instructions**
**Toevoegen:** Insert → Text → Label
**Eigenschappen:**
- **Text eigenschap:**
```powerapps
"We'll send a 6-digit verification code to your registered phone number."
```
- **Color eigenschap:** `RGBA(44, 62, 80, 1)`
- **Size eigenschap:** `12`
- **Height eigenschap:** `30`
- **Width eigenschap:** `400`
- **X eigenschap:** `483`
- **Y eigenschap:** `160`
- **Visible eigenschap:**
```powerapps
varResetMethod = "sms"
```

#### **13. Send SMS Button**
**Toevoegen:** Insert → Input → Button
**Eigenschappen:**
- **Text eigenschap:** `"📱 SEND SMS CODE"`
- **Fill eigenschap:** `RGBA(243, 156, 18, 1)`
- **Color eigenschap:** `RGBA(255, 255, 255, 1)`
- **Height eigenschap:** `40`
- **Width eigenschap:** `150`
- **X eigenschap:** `533`
- **Y eigenschap:** `200`
- **Visible eigenschap:**
```powerapps
varResetMethod = "sms" && !varSMSCodeSent
```
- **OnSelect eigenschap:**
```powerapps
// Simulate SMS sending
Set(varSMSCodeSent, true);
Set(varResetMessage, "SMS code sent to your registered phone number. Enter the 6-digit code below.");
Notify("SMS verification code sent", NotificationType.Success)
```

#### **14. SMS Code Input**
**Toevoegen:** Insert → Input → Text input
**Naam:** `SMSCodeInput`
**Eigenschappen:**
- **HintText eigenschap:** `"Enter 6-digit code"`
- **MaxLength eigenschap:** `6`
- **Height eigenschap:** `40`
- **Width eigenschap:** `150`
- **X eigenschap:** `533`
- **Y eigenschap:** `250`
- **Visible eigenschap:**
```powerapps
varResetMethod = "sms" && varSMSCodeSent
```

#### **15. Verify SMS Code Button**
**Toevoegen:** Insert → Input → Button
**Eigenschappen:**
- **Text eigenschap:** `"✅ VERIFY CODE"`
- **Fill eigenschap:** `RGBA(243, 156, 18, 1)`
- **Color eigenschap:** `RGBA(255, 255, 255, 1)`
- **Height eigenschap:** `40`
- **Width eigenschap:** `120`
- **X eigenschap:** `703`
- **Y eigenschap:** `250`
- **Visible eigenschap:**
```powerapps
varResetMethod = "sms" && varSMSCodeSent
```
- **OnSelect eigenschap:**
```powerapps
// Simulate SMS code verification (in real app, verify against sent code)
If(
    SMSCodeInput.Text = "123456", // Demo code
    Set(varResetSuccess, true);
    Set(varResetMessage, "SMS code verified! You can now reset your password.");
    Navigate(Password_Reset_Form_Screen, ScreenTransition.Fade),
    Set(varResetMessage, "Invalid SMS code. Please try again.")
)
```

---

### **STAP 7: FEEDBACK EN NAVIGATION**

#### **16. Message Display Label**
**Toevoegen:** Insert → Text → Label
**Eigenschappen:**
- **Text eigenschap:** `varResetMessage`
- **Color eigenschap:**
```powerapps
If(
    varResetSuccess,
    RGBA(40, 167, 69, 1),   // Green for success
    If(
        !IsBlank(varResetMessage),
        RGBA(220, 53, 69, 1), // Red for error
        RGBA(44, 62, 80, 1)   // Dark blue for info
    )
)
```
- **Size eigenschap:** `12`
- **Height eigenschap:** `60`
- **Width eigenschap:** `500`
- **X eigenschap:** `433`
- **Y eigenschap:** `490`

#### **17. Back to Login Button**
**Toevoegen:** Insert → Input → Button
**Eigenschappen:**
- **Text eigenschap:** `"🔙 BACK TO LOGIN"`
- **Fill eigenschap:** `RGBA(108, 117, 125, 1)`
- **Color eigenschap:** `RGBA(255, 255, 255, 1)`
- **Height eigenschap:** `40`
- **Width eigenschap:** `150`
- **X eigenschap:** `533`
- **Y eigenschap:** `570`
- **OnSelect eigenschap:**
```powerapps
Navigate(Login_Screen, ScreenTransition.Fade)
```

#### **18. Help Section**
**Toevoegen:** Insert → Icons → Rectangle
**Eigenschappen:**
- **Fill eigenschap:** `RGBA(169, 198, 232, 0.3)`
- **BorderColor eigenschap:** `RGBA(243, 156, 18, 1)`
- **BorderThickness eigenschap:** `1`
- **Height eigenschap:** `100`
- **Width eigenschap:** `500`
- **X eigenschap:** `433`
- **Y eigenschap:** `630`

**Toevoegen:** Insert → Text → Label (Help Text)
**Eigenschappen:**
- **Text eigenschap:**
```powerapps
"💡 Tips:" & Char(10) &
"• Check your spam folder for reset emails" & Char(10) &
"• Reset links expire in 24 hours" & Char(10) &
"• Contact support: <EMAIL> | +31 20 123 4567"
```
- **Color eigenschap:** `RGBA(44, 62, 80, 1)`
- **Size eigenschap:** `11`
- **Height eigenschap:** `80`
- **Width eigenschap:** `480`
- **X eigenschap:** `443`
- **Y eigenschap:** `640`

**De Forgot Password Screen is nu volledig geïmplementeerd met alle reset opties en security features!**
