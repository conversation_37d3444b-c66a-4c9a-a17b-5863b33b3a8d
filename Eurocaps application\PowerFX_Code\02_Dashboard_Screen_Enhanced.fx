# ************************************************************************************************
# Enhanced Dashboard Screen for EuroCaps Ordering System
# Volledig functioneel dashboard met real-time data en stakeholder-gebaseerde toegang
# Gebaseerd op mockup specificaties en database structuur
# ************************************************************************************************

# SCREEN PROPERTIES
Dashboard_Screen As screen:
    Fill: =RGBA(245, 245, 245, 1)  // Background color #f5f5f5
    LoadingSpinnerColor: =RGBA(74, 111, 165, 1)  // Header color #4a6fa5
    
    # OnVisible Event - Initialize dashboard data and check user permissions
    OnVisible: |
        =// Check if user is logged in
        If(
            IsBlank(varUserRole) || !varIsLoggedIn,
            Navigate(Login_Screen, ScreenTransition.Fade),
            
            // Initialize dashboard data based on user role
            // Load database collections
            ClearCollect(colCustomers,
                {CustomerID: 1, CustomerName: "Coffee World", ContactPerson: "David Lee", Email: "<EMAIL>", Phone: "+31 20 123 4567", Address: "Koffieweg 10, Amsterdam, 1012 AB"},
                {CustomerID: 2, CustomerName: "Bean Lovers", ContactPerson: "John Smith", Email: "<EMAIL>", Phone: "+31 30 234 5678", Address: "Bonenstraat 25, Utrecht, 3511 CD"},
                {CustomerID: 3, CustomerName: "Café Express", ContactPerson: "Maria Garcia", Email: "<EMAIL>", Phone: "+31 10 345 6789", Address: "Espressolaan 5, Rotterdam, 3011 EF"},
                {CustomerID: 4, CustomerName: "Morning Brew", ContactPerson: "Sarah Johnson", Email: "<EMAIL>", Phone: "+31 40 456 7890", Address: "Ochtendweg 15, Eindhoven, 5611 GH"},
                {CustomerID: 5, CustomerName: "The Daily Cup", ContactPerson: "Robert Brown", Email: "<EMAIL>", Phone: "+31 70 567 8901", Address: "Dagelijksestraat 30, Den Haag, 2511 IJ"}
            );
            
            ClearCollect(colOrders,
                {OrderID: 1, OrderNumber: "ORD-1080", CustomerID: 10, OrderDate: DateValue("2025-05-05"), DeliveryDate: DateValue("2025-05-12"), Status: "delivered", Notes: "Standard delivery"},
                {OrderID: 2, OrderNumber: "ORD-1081", CustomerID: 9, OrderDate: DateValue("2025-05-07"), DeliveryDate: DateValue("2025-05-14"), Status: "cancelled", Notes: "Customer cancelled"},
                {OrderID: 3, OrderNumber: "ORD-1082", CustomerID: 8, OrderDate: DateValue("2025-05-08"), DeliveryDate: DateValue("2025-05-15"), Status: "delivered", Notes: "Before noon"},
                {OrderID: 4, OrderNumber: "ORD-1083", CustomerID: 7, OrderDate: DateValue("2025-05-09"), DeliveryDate: DateValue("2025-05-16"), Status: "delivered", Notes: "Leave with reception"},
                {OrderID: 5, OrderNumber: "ORD-1084", CustomerID: 6, OrderDate: DateValue("2025-05-10"), DeliveryDate: DateValue("2025-05-17"), Status: "delivered", Notes: "Standard delivery"},
                {OrderID: 6, OrderNumber: "ORD-1085", CustomerID: 1, OrderDate: DateValue("2025-05-12"), DeliveryDate: DateValue("2025-05-19"), Status: "processing", Notes: "Urgent order"},
                {OrderID: 7, OrderNumber: "ORD-1086", CustomerID: 4, OrderDate: DateValue("2025-05-13"), DeliveryDate: DateValue("2025-05-20"), Status: "shipped", Notes: "Standard delivery"},
                {OrderID: 8, OrderNumber: "ORD-1087", CustomerID: 3, OrderDate: DateValue("2025-05-14"), DeliveryDate: DateValue("2025-05-21"), Status: "processing", Notes: "Call before delivery"},
                {OrderID: 9, OrderNumber: "ORD-1088", CustomerID: 2, OrderDate: DateValue("2025-05-14"), DeliveryDate: DateValue("2025-05-21"), Status: "new", Notes: "Standard delivery"},
                {OrderID: 10, OrderNumber: "ORD-1089", CustomerID: 1, OrderDate: DateValue("2025-05-15"), DeliveryDate: DateValue("2025-05-22"), Status: "new", Notes: "Before noon"}
            );
            
            // Calculate order statistics
            Set(varNewOrders, CountRows(Filter(colOrders, Status = "new")));
            Set(varProcessingOrders, CountRows(Filter(colOrders, Status = "processing")));
            Set(varShippedOrders, CountRows(Filter(colOrders, Status = "shipped")));
            Set(varDeliveredOrders, CountRows(Filter(colOrders, Status = "delivered")));
            
            // Set recent orders (last 5)
            ClearCollect(colRecentOrders, 
                FirstN(
                    SortByColumns(colOrders, "OrderDate", Descending),
                    5
                )
            )
        )

# HEADER BAR
HeaderBar As rectangle:
    Fill: =RGBA(74, 111, 165, 1)  // Header color #4a6fa5
    Height: =60
    Width: =Parent.Width
    X: =0
    Y: =0
    BorderThickness: =0

# HEADER LOGO
HeaderLogo As image:
    Height: =40
    Width: =40
    X: =20
    Y: =10
    # EuroCaps logo
    Image: ="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSJ3aGl0ZSIgcng9IjQiLz4KPHR5cGUgeD0iMjAiIHk9IjE1IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iOCIgZm9udC13ZWlnaHQ9ImJvbGQiIGZpbGw9IiM0YTZmYTUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPkVDPC90ZXh0Pgo8dGV4dCB4PSIyMCIgeT0iMzAiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSI2IiBmaWxsPSIjNGE2ZmE1IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5PcmRlcjwvdGV4dD4KPC9zdmc+"

# HEADER TITLE
HeaderTitle As label:
    Text: ="EuroCaps Ordering System"
    Font: =Font.Arial
    FontWeight: =FontWeight.Bold
    Size: =16
    Color: =RGBA(255, 255, 255, 1)
    Height: =40
    Width: =300
    X: =70
    Y: =10

# USER DROPDOWN BUTTON
UserDropdown As button:
    Text: =varUserName & " ▼"
    Font: =Font.Arial
    Size: =14
    Color: =RGBA(255, 255, 255, 1)
    Fill: =RGBA(0, 0, 0, 0)  // Transparent
    BorderColor: =RGBA(255, 255, 255, 1)
    BorderThickness: =1
    Height: =40
    Width: =150
    X: =Parent.Width - 270
    Y: =10
    OnSelect: =Set(varShowUserMenu, !varShowUserMenu)

# SETTINGS BUTTON
SettingsButton As button:
    Text: ="⚙ Settings"
    Font: =Font.Arial
    Size: =14
    Color: =RGBA(255, 255, 255, 1)
    Fill: =RGBA(0, 0, 0, 0)  // Transparent
    BorderColor: =RGBA(255, 255, 255, 1)
    BorderThickness: =1
    Height: =40
    Width: =100
    X: =Parent.Width - 110
    Y: =10
    OnSelect: =Navigate(Settings_Screen, ScreenTransition.Fade)

# NAVIGATION MENU SIDEBAR
NavigationMenu As rectangle:
    Fill: =RGBA(58, 90, 128, 1)  // Menu sidebar color #3a5a80
    Height: =Parent.Height - 60
    Width: =200
    X: =0
    Y: =60
    BorderThickness: =0

# MENU DASHBOARD (CURRENT PAGE - HIGHLIGHTED)
MenuDashboard As button:
    Text: ="≡ Dashboard"
    Font: =Font.Arial
    Size: =14
    Color: =RGBA(255, 255, 255, 1)
    Fill: =RGBA(74, 111, 165, 1)  // Highlighted color
    BorderColor: =RGBA(0, 0, 0, 0)
    Height: =50
    Width: =200
    X: =0
    Y: =60
    Align: =Align.Left
    # Current page - no navigation

# MENU CUSTOMERS
MenuCustomers As button:
    Text: ="👥 Customers"
    Font: =Font.Arial
    Size: =14
    Color: =RGBA(255, 255, 255, 1)
    Fill: =RGBA(58, 90, 128, 1)
    HoverFill: =RGBA(74, 111, 165, 1)
    BorderColor: =RGBA(0, 0, 0, 0)
    Height: =50
    Width: =200
    X: =0
    Y: =110
    Align: =Align.Left
    OnSelect: =Navigate(Customer_List_Screen, ScreenTransition.Fade)

# MENU PRODUCTS
MenuProducts As button:
    Text: ="📦 Products"
    Font: =Font.Arial
    Size: =14
    Color: =RGBA(255, 255, 255, 1)
    Fill: =RGBA(58, 90, 128, 1)
    HoverFill: =RGBA(74, 111, 165, 1)
    BorderColor: =RGBA(0, 0, 0, 0)
    Height: =50
    Width: =200
    X: =0
    Y: =160
    Align: =Align.Left
    OnSelect: =Navigate(Product_Catalog_Screen, ScreenTransition.Fade)

# MENU ORDERS
MenuOrders As button:
    Text: ="📋 Orders"
    Font: =Font.Arial
    Size: =14
    Color: =RGBA(255, 255, 255, 1)
    Fill: =RGBA(58, 90, 128, 1)
    HoverFill: =RGBA(74, 111, 165, 1)
    BorderColor: =RGBA(0, 0, 0, 0)
    Height: =50
    Width: =200
    X: =0
    Y: =210
    Align: =Align.Left
    OnSelect: =Navigate(Order_History_Screen, ScreenTransition.Fade)

# MENU LOGOUT
MenuLogout As button:
    Text: ="🚪 Logout"
    Font: =Font.Arial
    Size: =14
    Color: =RGBA(255, 255, 255, 1)
    Fill: =RGBA(58, 90, 128, 1)
    HoverFill: =RGBA(220, 53, 69, 1)  // Red hover for logout
    BorderColor: =RGBA(0, 0, 0, 0)
    Height: =50
    Width: =200
    X: =0
    Y: =Parent.Height - 50
    Align: =Align.Left
    OnSelect: |
        =// Clear user session
        Set(varUserRole, "");
        Set(varUserName, "");
        Set(varIsLoggedIn, false);
        Notify("Logged out successfully", NotificationType.Success);
        Navigate(Login_Screen, ScreenTransition.Fade)

# MAIN CONTENT AREA TITLE
PageTitle As label:
    Text: ="Dashboard"
    Font: =Font.Arial
    FontWeight: =FontWeight.Bold
    Size: =24
    Color: =RGBA(51, 51, 51, 1)
    Height: =40
    Width: =200
    X: =220
    Y: =80

# STATUS CARDS ROW 1
# NEW ORDERS CARD
NewOrdersCard As rectangle:
    Fill: =RGBA(255, 255, 255, 1)  // White background
    Height: =120
    Width: =200
    X: =220
    Y: =140
    BorderRadius: =8
    BorderColor: =RGBA(255, 152, 0, 1)  // Orange border #ff9800
    BorderThickness: =2

NewOrdersTitle As label:
    Text: ="NEW ORDERS"
    Font: =Font.Arial
    FontWeight: =FontWeight.Bold
    Size: =14
    Color: =RGBA(51, 51, 51, 1)
    Height: =30
    Width: =180
    X: =NewOrdersCard.X + 10
    Y: =NewOrdersCard.Y + 10
    Align: =Align.Center

NewOrdersCount As label:
    Text: =Text(varNewOrders)
    Font: =Font.Arial
    FontWeight: =FontWeight.Bold
    Size: =36
    Color: =RGBA(255, 152, 0, 1)  // Orange #ff9800
    Height: =60
    Width: =180
    X: =NewOrdersCard.X + 10
    Y: =NewOrdersCard.Y + 40
    Align: =Align.Center

NewOrdersButton As button:
    Text: =""
    Fill: =RGBA(0, 0, 0, 0)  // Transparent overlay for click
    BorderColor: =RGBA(0, 0, 0, 0)
    Height: =120
    Width: =200
    X: =220
    Y: =140
    OnSelect: =Navigate(Order_History_Screen, ScreenTransition.Fade, {FilterStatus: "new"})

# PROCESSING ORDERS CARD
ProcessingOrdersCard As rectangle:
    Fill: =RGBA(255, 255, 255, 1)
    Height: =120
    Width: =200
    X: =440
    Y: =140
    BorderRadius: =8
    BorderColor: =RGBA(74, 111, 165, 1)  // Blue border #4a6fa5
    BorderThickness: =2

ProcessingOrdersTitle As label:
    Text: ="PROCESSING ORDERS"
    Font: =Font.Arial
    FontWeight: =FontWeight.Bold
    Size: =14
    Color: =RGBA(51, 51, 51, 1)
    Height: =30
    Width: =180
    X: =ProcessingOrdersCard.X + 10
    Y: =ProcessingOrdersCard.Y + 10
    Align: =Align.Center

ProcessingOrdersCount As label:
    Text: =Text(varProcessingOrders)
    Font: =Font.Arial
    FontWeight: =FontWeight.Bold
    Size: =36
    Color: =RGBA(74, 111, 165, 1)  // Blue #4a6fa5
    Height: =60
    Width: =180
    X: =ProcessingOrdersCard.X + 10
    Y: =ProcessingOrdersCard.Y + 40
    Align: =Align.Center

ProcessingOrdersButton As button:
    Text: =""
    Fill: =RGBA(0, 0, 0, 0)
    BorderColor: =RGBA(0, 0, 0, 0)
    Height: =120
    Width: =200
    X: =440
    Y: =140
    OnSelect: =Navigate(Order_History_Screen, ScreenTransition.Fade, {FilterStatus: "processing"})

# STATUS CARDS ROW 2
# SHIPPED ORDERS CARD
ShippedOrdersCard As rectangle:
    Fill: =RGBA(255, 255, 255, 1)
    Height: =120
    Width: =200
    X: =220
    Y: =280
    BorderRadius: =8
    BorderColor: =RGBA(156, 39, 176, 1)  // Purple border #9c27b0
    BorderThickness: =2

ShippedOrdersTitle As label:
    Text: ="SHIPPED ORDERS"
    Font: =Font.Arial
    FontWeight: =FontWeight.Bold
    Size: =14
    Color: =RGBA(51, 51, 51, 1)
    Height: =30
    Width: =180
    X: =ShippedOrdersCard.X + 10
    Y: =ShippedOrdersCard.Y + 10
    Align: =Align.Center

ShippedOrdersCount As label:
    Text: =Text(varShippedOrders)
    Font: =Font.Arial
    FontWeight: =FontWeight.Bold
    Size: =36
    Color: =RGBA(156, 39, 176, 1)  // Purple #9c27b0
    Height: =60
    Width: =180
    X: =ShippedOrdersCard.X + 10
    Y: =ShippedOrdersCard.Y + 40
    Align: =Align.Center

ShippedOrdersButton As button:
    Text: =""
    Fill: =RGBA(0, 0, 0, 0)
    BorderColor: =RGBA(0, 0, 0, 0)
    Height: =120
    Width: =200
    X: =220
    Y: =280
    OnSelect: =Navigate(Order_History_Screen, ScreenTransition.Fade, {FilterStatus: "shipped"})

# DELIVERED ORDERS CARD
DeliveredOrdersCard As rectangle:
    Fill: =RGBA(255, 255, 255, 1)
    Height: =120
    Width: =200
    X: =440
    Y: =280
    BorderRadius: =8
    BorderColor: =RGBA(76, 175, 80, 1)  // Green border #4caf50
    BorderThickness: =2

DeliveredOrdersTitle As label:
    Text: ="DELIVERED ORDERS"
    Font: =Font.Arial
    FontWeight: =FontWeight.Bold
    Size: =14
    Color: =RGBA(51, 51, 51, 1)
    Height: =30
    Width: =180
    X: =DeliveredOrdersCard.X + 10
    Y: =DeliveredOrdersCard.Y + 10
    Align: =Align.Center

DeliveredOrdersCount As label:
    Text: =Text(varDeliveredOrders)
    Font: =Font.Arial
    FontWeight: =FontWeight.Bold
    Size: =36
    Color: =RGBA(76, 175, 80, 1)  // Green #4caf50
    Height: =60
    Width: =180
    X: =DeliveredOrdersCard.X + 10
    Y: =DeliveredOrdersCard.Y + 40
    Align: =Align.Center

DeliveredOrdersButton As button:
    Text: =""
    Fill: =RGBA(0, 0, 0, 0)
    BorderColor: =RGBA(0, 0, 0, 0)
    Height: =120
    Width: =200
    X: =440
    Y: =280
    OnSelect: =Navigate(Order_History_Screen, ScreenTransition.Fade, {FilterStatus: "delivered"})

# RECENT ORDERS SECTION
RecentOrdersTitle As label:
    Text: ="RECENT ORDERS"
    Font: =Font.Arial
    FontWeight: =FontWeight.Bold
    Size: =18
    Color: =RGBA(51, 51, 51, 1)
    Height: =30
    Width: =420
    X: =220
    Y: =420

# RECENT ORDERS TABLE CONTAINER
RecentOrdersTable As rectangle:
    Fill: =RGBA(255, 255, 255, 1)
    Height: =200
    Width: =420
    X: =220
    Y: =460
    BorderRadius: =8
    BorderColor: =RGBA(204, 204, 204, 1)
    BorderThickness: =1

# RECENT ORDERS GALLERY
RecentOrdersGallery As gallery:
    Items: =colRecentOrders
    Height: =180
    Width: =400
    X: =230
    Y: =470
    TemplateSize: =35
    BorderThickness: =0
    Fill: =RGBA(255, 255, 255, 1)

    # Gallery Template
    Template:
        # Order Number
        OrderNumberLabel As label:
            Text: =ThisItem.OrderNumber
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(51, 51, 51, 1)
            Height: =30
            Width: =80
            X: =5
            Y: =2

        # Customer Name (lookup from customers)
        CustomerNameLabel As label:
            Text: =LookUp(colCustomers, CustomerID = ThisItem.CustomerID).CustomerName
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(51, 51, 51, 1)
            Height: =30
            Width: =120
            X: =90
            Y: =2

        # Order Date
        OrderDateLabel As label:
            Text: =Text(ThisItem.OrderDate, "dd/mm/yyyy")
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(51, 51, 51, 1)
            Height: =30
            Width: =80
            X: =215
            Y: =2

        # Status with color coding
        StatusLabel As label:
            Text: =ThisItem.Status
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =12
            Color: =Switch(
                ThisItem.Status,
                "new", RGBA(255, 152, 0, 1),      // Orange
                "processing", RGBA(74, 111, 165, 1), // Blue
                "shipped", RGBA(156, 39, 176, 1),    // Purple
                "delivered", RGBA(76, 175, 80, 1),   // Green
                RGBA(51, 51, 51, 1)                  // Default gray
            )
            Height: =30
            Width: =80
            X: =300
            Y: =2

        # Row separator
        RowSeparator As rectangle:
            Fill: =RGBA(240, 240, 240, 1)
            Height: =1
            Width: =380
            X: =10
            Y: =32

    OnSelect: =Navigate(Order_Details_Screen, ScreenTransition.Fade, {SelectedOrderID: ThisItem.OrderID})

# RECENT ORDERS TABLE HEADERS
OrderNumberHeader As label:
    Text: ="Order #"
    Font: =Font.Arial
    FontWeight: =FontWeight.Bold
    Size: =12
    Color: =RGBA(51, 51, 51, 1)
    Height: =25
    Width: =80
    X: =235
    Y: =465

CustomerHeader As label:
    Text: ="Customer"
    Font: =Font.Arial
    FontWeight: =FontWeight.Bold
    Size: =12
    Color: =RGBA(51, 51, 51, 1)
    Height: =25
    Width: =120
    X: =320
    Y: =465

DateHeader As label:
    Text: ="Date"
    Font: =Font.Arial
    FontWeight: =FontWeight.Bold
    Size: =12
    Color: =RGBA(51, 51, 51, 1)
    Height: =25
    Width: =80
    X: =445
    Y: =465

StatusHeader As label:
    Text: ="Status"
    Font: =Font.Arial
    FontWeight: =FontWeight.Bold
    Size: =12
    Color: =RGBA(51, 51, 51, 1)
    Height: =25
    Width: =80
    X: =530
    Y: =465

# QUICK ACTIONS SECTION
QuickActionsTitle As label:
    Text: ="QUICK ACTIONS"
    Font: =Font.Arial
    FontWeight: =FontWeight.Bold
    Size: =18
    Color: =RGBA(51, 51, 51, 1)
    Height: =30
    Width: =420
    X: =220
    Y: =680

# NEW ORDER BUTTON (PRIMARY)
NewOrderButton As button:
    Text: ="+ NEW ORDER"
    Font: =Font.Arial
    FontWeight: =FontWeight.Bold
    Size: =14
    Color: =RGBA(255, 255, 255, 1)
    Fill: =RGBA(243, 156, 18, 1)  // Orange #F39C12
    HoverFill: =RGBA(230, 140, 5, 1)
    BorderColor: =RGBA(243, 156, 18, 1)
    BorderThickness: =0
    BorderRadius: =4
    Height: =45
    Width: =130
    X: =220
    Y: =720
    OnSelect: =Navigate(New_Order_Screen, ScreenTransition.Fade)

# VIEW CUSTOMERS BUTTON
ViewCustomersButton As button:
    Text: ="VIEW CUSTOMERS"
    Font: =Font.Arial
    FontWeight: =FontWeight.Bold
    Size: =14
    Color: =RGBA(74, 111, 165, 1)
    Fill: =RGBA(255, 255, 255, 1)
    HoverFill: =RGBA(245, 245, 245, 1)
    BorderColor: =RGBA(74, 111, 165, 1)
    BorderThickness: =2
    BorderRadius: =4
    Height: =45
    Width: =140
    X: =365
    Y: =720
    OnSelect: =Navigate(Customer_List_Screen, ScreenTransition.Fade)

# VIEW PRODUCTS BUTTON
ViewProductsButton As button:
    Text: ="VIEW PRODUCTS"
    Font: =Font.Arial
    FontWeight: =FontWeight.Bold
    Size: =14
    Color: =RGBA(74, 111, 165, 1)
    Fill: =RGBA(255, 255, 255, 1)
    HoverFill: =RGBA(245, 245, 245, 1)
    BorderColor: =RGBA(74, 111, 165, 1)
    BorderThickness: =2
    BorderRadius: =4
    Height: =45
    Width: =130
    X: =520
    Y: =720
    OnSelect: =Navigate(Product_Catalog_Screen, ScreenTransition.Fade)
