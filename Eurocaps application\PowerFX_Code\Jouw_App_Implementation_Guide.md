# PowerApps Implementation Guide - Voor Jouw EuroCaps App

## Stap 1: Login_Schreen OnVisible Event

1. **Selecteer je Login_Schreen**
2. **Ga naar Advanced properties**
3. **Zoek OnVisible en plak deze code:**

```powerfx
// Initialize login variables
Set(varUserRole, "");
Set(varUserName, "");
Set(varIsLoggedIn, false);
Set(varRememberMe, false);
Set(varLoginAttempts, 0);

// Check if user credentials are remembered
If(
    !IsBlank(Get("RememberedUser")),
    Set(varRememberMe, true);
    Set(Hint_Username.Text, Get("RememberedUser"));
    Set(Checkbox1.Value, true)
);

// Initialize stakeholder data for authentication
ClearCollect(colStakeholders,
    {StakeholderID: 1, Username: "admin", Password: "admin123", Role: "Administrator", Name: "System Administrator", AccessLevel: "Full"},
    {StakeholderID: 2, Username: "manager", Password: "manager123", Role: "Manager", Name: "Operations Manager", AccessLevel: "Management"},
    {StakeholderID: 3, Username: "sales", Password: "sales123", Role: "Sales", Name: "Sales Representative", AccessLevel: "Sales"},
    {StakeholderID: 4, Username: "customer", Password: "customer123", Role: "Customer", Name: "Customer User", AccessLevel: "Limited"},
    {StakeholderID: 5, Username: "supplier", Password: "supplier123", Role: "Supplier", Name: "Supplier Contact", AccessLevel: "Supplier"}
)
```

## Stap 2: Button1 (Login Button) OnSelect Event

1. **Selecteer je Button1 (Login button)**
2. **Ga naar Advanced properties**
3. **Zoek OnSelect en vervang de huidige code met:**

```powerfx
// Increment login attempts
Set(varLoginAttempts, varLoginAttempts + 1);

// Validate input fields
If(
    IsBlank(Hint_Username.Text) || IsBlank(Hint_Password.Text),
    Notify("Please enter both username and password", NotificationType.Error),
    
    // Check credentials against stakeholder database
    With(
        LookUp(colStakeholders, Username = Hint_Username.Text && Password = Hint_Password.Text),
        If(
            IsBlank(ThisRecord),
            // Invalid credentials
            Notify("Invalid username or password", NotificationType.Error);
            Set(varLoginAttempts, varLoginAttempts + 1),
            
            // Valid credentials - set user variables
            Set(varUserName, ThisRecord.Name);
            Set(varUserRole, ThisRecord.Role);
            Set(varAccessLevel, ThisRecord.AccessLevel);
            Set(varIsLoggedIn, true);
            
            // Handle Remember Me functionality
            If(
                Checkbox1.Value,
                Set("RememberedUser", Hint_Username.Text),
                Remove("RememberedUser")
            );
            
            // Show success message and navigate to dashboard
            Notify("Login successful! Welcome " & varUserName, NotificationType.Success);
            Navigate(Dashboard_Screen, ScreenTransition.Fade)
        )
    )
)
```

## Stap 3: Button1_1 (Forgot Password) OnSelect Event

1. **Selecteer je Button1_1 (Forgot Password button)**
2. **Vervang OnSelect code met:**

```powerfx
Navigate(Settings_Screen, ScreenTransition.Fade)
```

## Stap 4: Input Field Validation (Optioneel)

### Voor Hint_Username BorderColor:
```powerfx
If(IsBlank(Self.Text) && varLoginAttempts > 0, RGBA(255, 0, 0, 1), RGBA(243, 156, 18, 1))
```

### Voor Hint_Password BorderColor:
```powerfx
If(IsBlank(Self.Text) && varLoginAttempts > 0, RGBA(255, 0, 0, 1), RGBA(243, 156, 18, 1))
```

## Stap 5: Test Credentials

Na implementatie kun je inloggen met:

- **Administrator**: 
  - Username: `admin`
  - Password: `admin123`

- **Manager**: 
  - Username: `manager`
  - Password: `manager123`

- **Sales**: 
  - Username: `sales`
  - Password: `sales123`

- **Customer**: 
  - Username: `customer`
  - Password: `customer123`

- **Supplier**: 
  - Username: `supplier`
  - Password: `supplier123`

## Stap 6: Dashboard_Screen Setup

Voor je Dashboard_Screen (als je die hebt):

### OnVisible Event:
```powerfx
// Check if user is logged in
If(
    IsBlank(varUserRole) || !varIsLoggedIn,
    Navigate(Login_Schreen, ScreenTransition.Fade),
    
    // Initialize dashboard data
    // Load customer data
    ClearCollect(colCustomers,
        {CustomerID: 1, CustomerName: "Coffee World", ContactPerson: "David Lee", Email: "<EMAIL>"},
        {CustomerID: 2, CustomerName: "Bean Lovers", ContactPerson: "John Smith", Email: "<EMAIL>"},
        {CustomerID: 3, CustomerName: "Café Express", ContactPerson: "Maria Garcia", Email: "<EMAIL>"},
        {CustomerID: 4, CustomerName: "Morning Brew", ContactPerson: "Sarah Johnson", Email: "<EMAIL>"},
        {CustomerID: 5, CustomerName: "The Daily Cup", ContactPerson: "Robert Brown", Email: "<EMAIL>"}
    );
    
    // Load order data
    ClearCollect(colOrders,
        {OrderID: 1, OrderNumber: "ORD-1080", CustomerID: 1, OrderDate: DateValue("2025-05-05"), Status: "delivered"},
        {OrderID: 2, OrderNumber: "ORD-1081", CustomerID: 2, OrderDate: DateValue("2025-05-07"), Status: "cancelled"},
        {OrderID: 3, OrderNumber: "ORD-1082", CustomerID: 3, OrderDate: DateValue("2025-05-08"), Status: "delivered"},
        {OrderID: 4, OrderNumber: "ORD-1083", CustomerID: 4, OrderDate: DateValue("2025-05-09"), Status: "delivered"},
        {OrderID: 5, OrderNumber: "ORD-1084", CustomerID: 5, OrderDate: DateValue("2025-05-10"), Status: "delivered"},
        {OrderID: 6, OrderNumber: "ORD-1085", CustomerID: 1, OrderDate: DateValue("2025-05-12"), Status: "processing"},
        {OrderID: 7, OrderNumber: "ORD-1086", CustomerID: 4, OrderDate: DateValue("2025-05-13"), Status: "shipped"},
        {OrderID: 8, OrderNumber: "ORD-1087", CustomerID: 3, OrderDate: DateValue("2025-05-14"), Status: "processing"},
        {OrderID: 9, OrderNumber: "ORD-1088", CustomerID: 2, OrderDate: DateValue("2025-05-14"), Status: "new"},
        {OrderID: 10, OrderNumber: "ORD-1089", CustomerID: 1, OrderDate: DateValue("2025-05-15"), Status: "new"}
    );
    
    // Calculate order statistics
    Set(varNewOrders, CountRows(Filter(colOrders, Status = "new")));
    Set(varProcessingOrders, CountRows(Filter(colOrders, Status = "processing")));
    Set(varShippedOrders, CountRows(Filter(colOrders, Status = "shipped")));
    Set(varDeliveredOrders, CountRows(Filter(colOrders, Status = "delivered")))
)
```

## Stap 7: Testing

1. **Save en Publish** je app
2. **Test de login** met een van de credentials
3. **Check of je wordt doorgestuurd** naar Dashboard_Screen
4. **Test Remember Me** functionaliteit
5. **Test Forgot Password** button

## Troubleshooting

### Als login niet werkt:
1. Check of alle variabelen correct zijn gespeld
2. Controleer of colStakeholders collection wordt geladen
3. Gebruik Monitor tool om te debuggen

### Als navigation niet werkt:
1. Zorg dat Dashboard_Screen bestaat
2. Check screen namen (case-sensitive)

### Als Remember Me niet werkt:
1. Check of Checkbox1.Value correct wordt gelezen
2. Test Set/Get functies

## Volgende Stappen

Na succesvolle login implementatie:
1. Implementeer Dashboard functionaliteit
2. Voeg Customer List toe
3. Implementeer Product Catalog
4. Voeg Order Management toe

Alle verdere schermen volgen hetzelfde patroon met OnVisible events en button OnSelect events.
