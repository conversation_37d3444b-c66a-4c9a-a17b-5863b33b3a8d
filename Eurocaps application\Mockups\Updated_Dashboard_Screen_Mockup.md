# 📊 UPDATED DASHBOARD SCREEN MOCKUP
## Enhanced Dashboard met Database Integratie & Supply Chain Metrics

---

## 🎨 **VISUAL MOCKUP BESCHRIJVING**

```
┌─────────────────────────────────────────────────────────────────────────────────────────────────────┐
│ [🏢 Logo] EuroCaps Order Management Pro                    [🔔 3] [👤 Admin User ▼] [⚙️] [🚪]    │
└─────────────────────────────────────────────────────────────────────────────────────────────────────┘
┌─────────────┬───────────────────────────────────────────────────────────────────────────────────────┐
│ ≡ Dashboard │ 📊 DASHBOARD - SUPPLY CHAIN OVERVIEW                    🕒 Last Updated: 14:32 CET │
│ 👥 Customers│                                                                                       │
│ 📦 Products │ ┌─────────────┬─────────────┬─────────────┬─────────────┬─────────────┬─────────────┐ │
│ 📋 Orders   │ │ 📋 NEW      │ 👥 ACTIVE   │ ⚠️ LOW      │ 💰 REVENUE  │ 🚚 PENDING  │ 📈 GROWTH   │ │
│ 🏭 Materials│ │ ORDERS      │ CUSTOMERS   │ STOCK       │ TODAY       │ DELIVERIES │ THIS MONTH  │ │
│ 🚚 Suppliers│ │             │             │ ALERTS      │             │             │             │ │
│ 📊 Reports  │ │    12       │    45       │     8       │  €2,847     │     6       │   +15.3%    │ │
│ ⚙️ Settings │ │             │             │             │             │             │             │ │
│             │ └─────────────┴─────────────┴─────────────┴─────────────┴─────────────┴─────────────┘ │
│             │                                                                                       │
│             │ ┌─────────────────────────────────────────┬─────────────────────────────────────────┐ │
│             │ │          📋 RECENT ORDERS               │         🏭 INVENTORY STATUS             │ │
│             │ │                                         │                                         │ │
│             │ │ ORD-1089  Coffee World    €49.90  NEW  │ ☑️ Coffee Beans      500kg    Normal    │ │
│             │ │ ORD-1088  Bean Lovers    €89.90  PROC  │ ⚠️ Aluminum Caps    1,200pcs  Low       │ │
│             │ │ ORD-1087  Café Express   €54.90  SHIP  │ ❌ Vanilla Flavor      8L     Critical  │ │
│             │ │ ORD-1086  Morning Brew  €129.80  DELIV │ ☑️ Cardboard Boxes   800pcs   Normal    │ │
│             │ │ ORD-1085  Espresso Cent   €0.00  CANC  │ ⚠️ Caramel Flavor     25L     Low       │ │
│             │ │                                         │                                         │ │
│             │ │ [📋 View All Orders]                    │ [🏭 Manage Inventory]                   │ │
│             │ └─────────────────────────────────────────┴─────────────────────────────────────────┘ │
│             │                                                                                       │
│             │ ┌─────────────────────────────────────────┬─────────────────────────────────────────┐ │
│             │ │        🚚 SUPPLIER PERFORMANCE          │         📈 PRODUCTION METRICS           │ │
│             │ │                                         │                                         │ │
│             │ │ Premium Coffee Co.    ⭐4.8  €1,250    │ 📊 Orders Processed Today:      18      │ │
│             │ │ Aluminum Solutions    ⭐4.6  €890      │ 🎯 Production Capacity:        85%      │ │
│             │ │ PackTech Europe       ⭐4.2  €650      │ ⏱️ Avg Processing Time:       2.3h      │ │
│             │ │ FlavorMasters Intl    ⭐4.9  €420      │ 📦 Products Ready to Ship:     24      │ │
│             │ │                                         │ 🔄 Materials Reorder Queue:    3       │ │
│             │ │ [🚚 Manage Suppliers]                   │                                         │ │
│             │ │                                         │ [📈 View Detailed Reports]              │ │
│             │ └─────────────────────────────────────────┴─────────────────────────────────────────┘ │
│             │                                                                                       │
│             │ ┌─────────────────────────────────────────────────────────────────────────────────┐ │
│             │ │                           🎯 QUICK ACTIONS                                      │ │
│             │ │                                                                                 │ │
│             │ │ [📋 + New Order]  [👥 + New Customer]  [🏭 Order Materials]  [📊 Generate Report] │ │
│             │ │                                                                                 │ │
│             │ │ [🔔 View Alerts]  [📱 Send Notifications]  [⚙️ System Settings]  [📞 Contact Support] │ │
│             │ └─────────────────────────────────────────────────────────────────────────────────┘ │
│             │                                                                                       │
│             │ ┌─────────────────────────────────────────────────────────────────────────────────┐ │
│             │ │                        📊 PERFORMANCE CHARTS                                    │ │
│             │ │                                                                                 │ │
│             │ │ [📈 Sales Trend]     [📦 Inventory Levels]     [🚚 Delivery Performance]        │ │
│             │ │                                                                                 │ │
│             │ │ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐                   │ │
│             │ │ │   Sales Chart   │ │ Inventory Chart │ │ Delivery Chart  │                   │ │
│             │ │ │      📈         │ │       📊        │ │       🚚        │                   │ │
│             │ │ │                 │ │                 │ │                 │                   │ │
│             │ │ └─────────────────┘ └─────────────────┘ └─────────────────┘                   │ │
│             │ └─────────────────────────────────────────────────────────────────────────────────┘ │
└─────────────┴───────────────────────────────────────────────────────────────────────────────────────┘
```

---

## 🔧 **ENHANCED FUNCTIONAL REQUIREMENTS**

### **Real-time Metrics Cards:**

#### **New Orders Card:**
- **Data Source**: `CountRows(Filter(colOrders, Status = "new"))`
- **Update Frequency**: Real-time
- **Click Action**: Navigate to Order History with "new" filter
- **Color Coding**: Orange for attention
- **Trend Indicator**: Show increase/decrease from yesterday

#### **Active Customers Card:**
- **Data Source**: `CountRows(Filter(colCustomers, Status = "Active"))`
- **Update Frequency**: Daily
- **Click Action**: Navigate to Customer List
- **Additional Info**: Show new customers this month
- **Growth Metric**: Percentage change from last month

#### **Low Stock Alerts Card:**
- **Data Source**: `CountRows(Filter(colRawMaterials, StockLevel <= ReorderPoint))`
- **Update Frequency**: Real-time
- **Color Coding**: Red if > 0, Green if 0
- **Click Action**: Navigate to Raw Materials with "Low Stock" filter
- **Critical Items**: Show count of critical items (StockLevel = 0)

#### **Revenue Today Card:**
- **Data Source**: `Sum(Filter(colOrders, OrderDate = Today()), TotalAmount)`
- **Update Frequency**: Real-time
- **Format**: Currency with locale formatting
- **Comparison**: Show vs. yesterday and target
- **Trend**: Visual indicator (up/down arrow)

#### **Pending Deliveries Card:**
- **Data Source**: `CountRows(Filter(colOrders, Status = "shipped"))`
- **Update Frequency**: Real-time
- **Click Action**: Navigate to Order History with "shipped" filter
- **Urgency**: Highlight overdue deliveries
- **Timeline**: Show delivery dates

#### **Growth This Month Card:**
- **Data Source**: Calculated percentage growth
- **Metrics**: Revenue, orders, customers
- **Comparison**: Month-over-month growth
- **Visual**: Green for positive, red for negative
- **Drill-down**: Detailed growth analysis

---

## 🏭 **INVENTORY STATUS SECTION**

### **Real-time Inventory Display:**
- **Material Name**: From colRawMaterials
- **Current Stock**: Live stock levels
- **Status Indicators**:
  - ☑️ Normal (Green): Stock > ReorderPoint * 1.5
  - ⚠️ Low (Yellow): Stock <= ReorderPoint but > 0
  - ❌ Critical (Red): Stock = 0
- **Unit Display**: Show appropriate units (kg, pcs, L)
- **Quick Actions**: Reorder button for low stock items

### **Inventory Calculations:**
```powerapps
// Stock Status Calculation
AddColumns(
    colRawMaterials,
    "StockStatus",
    If(StockLevel = 0, "Critical",
       If(StockLevel <= ReorderPoint, "Low", "Normal")),
    "StatusIcon",
    If(StockLevel = 0, "❌",
       If(StockLevel <= ReorderPoint, "⚠️", "☑️")),
    "StatusColor",
    If(StockLevel = 0, RGBA(220, 53, 69, 1),
       If(StockLevel <= ReorderPoint, RGBA(255, 193, 7, 1), RGBA(40, 167, 69, 1)))
)
```

---

## 🚚 **SUPPLIER PERFORMANCE SECTION**

### **Supplier Metrics Display:**
- **Supplier Name**: From colSuppliers
- **Rating**: Star rating (1-5 stars)
- **Monthly Spend**: Total orders this month
- **Performance Indicators**:
  - Delivery time
  - Quality rating
  - Cost competitiveness
- **Quick Actions**: Contact supplier, view details, place order

### **Performance Calculations:**
```powerapps
// Supplier Performance Metrics
AddColumns(
    colSuppliers,
    "MonthlySpend", Sum(Filter(colMaterialOrders, 
        SupplierID = ThisRecord.SupplierID && 
        OrderDate >= DateAdd(Today(), -30, Days)), TotalAmount),
    "StarDisplay", Concatenate(Repeat("⭐", RoundUp(Rating, 0)), 
        Text(Rating, "0.0")),
    "PerformanceColor", If(Rating >= 4.5, RGBA(40, 167, 69, 1),
        If(Rating >= 4.0, RGBA(255, 193, 7, 1), RGBA(220, 53, 69, 1)))
)
```

---

## 📈 **PRODUCTION METRICS SECTION**

### **Real-time Production Data:**
- **Orders Processed Today**: Count of completed orders
- **Production Capacity**: Percentage of maximum capacity used
- **Average Processing Time**: Time from order to ready-to-ship
- **Products Ready to Ship**: Count of completed products
- **Materials Reorder Queue**: Count of materials needing reorder

### **Production Calculations:**
```powerapps
// Production Metrics
Set(varOrdersProcessedToday, 
    CountRows(Filter(colOrders, 
        DateValue(Text(LastModified)) = Today() && 
        Status in ["processing", "shipped", "delivered"])));

Set(varProductionCapacity, 
    (varOrdersProcessedToday / varMaxDailyCapacity) * 100);

Set(varAvgProcessingTime, 
    Average(Filter(colOrders, Status = "delivered"), 
        DateDiff(OrderDate, DeliveryDate, Hours)));
```

---

## 🎯 **QUICK ACTIONS SECTION**

### **Primary Actions:**
- **New Order**: Navigate to New Order Screen
- **New Customer**: Navigate to Customer Registration
- **Order Materials**: Navigate to Material Order Screen
- **Generate Report**: Open Reports & Analytics

### **Secondary Actions:**
- **View Alerts**: Show all system alerts
- **Send Notifications**: Bulk notification system
- **System Settings**: Navigate to Settings Screen
- **Contact Support**: Open support contact form

---

## 📊 **PERFORMANCE CHARTS SECTION**

### **Sales Trend Chart:**
- **Data**: Daily sales for last 30 days
- **Type**: Line chart with trend line
- **Metrics**: Revenue, order count, average order value
- **Interactive**: Click to drill down to specific day

### **Inventory Levels Chart:**
- **Data**: Current stock levels vs. reorder points
- **Type**: Bar chart with color coding
- **Categories**: By material type (Coffee, Packaging, Flavoring)
- **Alerts**: Visual indicators for low stock

### **Delivery Performance Chart:**
- **Data**: On-time delivery percentage
- **Type**: Gauge chart
- **Metrics**: Daily, weekly, monthly performance
- **Targets**: Show performance vs. targets

---

## 🔔 **NOTIFICATION SYSTEM**

### **Alert Types:**
- **Critical Stock**: Red badge for zero stock items
- **Low Stock**: Yellow badge for low stock warnings
- **Overdue Orders**: Orange badge for delayed orders
- **System Issues**: Blue badge for system notifications

### **Notification Display:**
- **Badge Count**: Number in header notification icon
- **Priority**: Color coding by urgency
- **Auto-refresh**: Update every 30 seconds
- **Click Action**: Show detailed alert list

---

## 🎨 **ENHANCED STYLING SPECIFICATIONS**

### **Color Scheme:**
- **Header**: #2C3E50 (Dark blue)
- **Sidebar**: #2C3E50 (Dark blue)
- **Background**: #1B3A4B (Dark blue-gray)
- **Cards**: #A9C6E8 (Light blue)
- **Primary Actions**: #F39C12 (Orange)
- **Success**: #28A745 (Green)
- **Warning**: #FFC107 (Yellow)
- **Danger**: #DC3545 (Red)
- **Info**: #17A2B8 (Blue)

### **Card Styling:**
- **Border Radius**: 8px
- **Border**: 2px solid #F39C12
- **Shadow**: Subtle drop shadow
- **Hover Effect**: Slight elevation
- **Padding**: 16px
- **Margin**: 8px between cards

### **Typography:**
- **Card Headers**: Arial Bold, 14pt, #2C3E50
- **Metrics**: Arial Bold, 24pt, #F39C12
- **Labels**: Arial Regular, 12pt, #2C3E50
- **Status Text**: Arial Regular, 11pt, color-coded

---

## 📱 **RESPONSIVE BEHAVIOR**

### **Desktop (1366x768):**
- **6-column metrics**: All cards visible
- **4-section layout**: Orders, Inventory, Suppliers, Production
- **3-chart display**: Side-by-side charts

### **Tablet (768x1024):**
- **3-column metrics**: Cards wrap to second row
- **2-section layout**: Stacked sections
- **Single chart**: Swipeable chart carousel

### **Mobile (375x667):**
- **Single column**: All elements stacked
- **Collapsible sections**: Expandable content areas
- **Simplified metrics**: Essential metrics only

**Deze enhanced dashboard mockup vormt de basis voor een complete supply chain management interface in PowerApps.**
