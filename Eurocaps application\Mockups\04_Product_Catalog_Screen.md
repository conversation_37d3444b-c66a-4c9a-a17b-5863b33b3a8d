# Product Catalog Screen - EuroCaps Ordering System

## Screen Layout

```
+---------------------------------------------------------------+
| [Logo] EuroCaps Ordering System           [User ▼] [Settings] |
+---------------------------------------------------------------+
| [≡ MENU]  | Products                                          |
|           |                                                    |
| Dashboard | [Search Products...                       ] [🔍]  |
| Customers |                                                    |
| Products  | FILTERS:                                           |
| Orders    | Type: [All ▼] Size: [All ▼] [Sort: Name ▼] [Reset]|
| Reports   |                                                    |
|           | +------------------------------------------------+ |
| Settings  | |  [Image]  |  [Image]  |  [Image]  |  [Image]  | |
| Logout    | |  Espresso |  Lungo    |  Ristretto|  Vanilla  | |
|           | |  Classic  |  Intense  |  Strong   |  Flavored | |
|           | |  Size: 10 |  Size: 20 |  Size: 10 |  Size: 20 | |
|           | |  [+ Add]  |  [+ Add]  |  [+ Add]  |  [+ Add]  | |
|           | +------------------------------------------------+ |
|           | |  [Image]  |  [Image]  |  [Image]  |  [Image]  | |
|           | |  Caramel  |  Decaf    |  Organic  |  Premium  | |
|           | |  Flavored |  Espresso |  Lungo    |  Espresso | |
|           | |  Size: 10 |  Size: 10 |  Size: 44 |  Size: 20 | |
|           | |  [+ Add]  |  [+ Add]  |  [+ Add]  |  [+ Add]  | |
|           | +------------------------------------------------+ |
|           |                                                    |
|           | [◀ Previous]                          [Next ▶]    |
|           | Showing 1-8 of 24 products                        |
|           |                                                    |
+---------------------------------------------------------------+
```

## Design Elements

### Colors
- Header: Blue (#4a6fa5)
- Menu sidebar: Dark blue (#3a5a80)
- Background: Light gray (#f5f5f5)
- Product cards: White (#ffffff)
- Product types:
  - Espresso: Dark brown (#5d4037)
  - Lungo: Medium brown (#8d6e63)
  - Ristretto: Black (#212121)
  - Flavored: Various accent colors

### Typography
- Header: Arial, 16pt, Bold, White
- Menu items: Arial, 14pt, White
- Page title: Arial, 18pt, Bold, Dark gray
- Product name: Arial, 14pt, Bold
- Product type: Arial, 12pt, Italic
- Product size: Arial, 12pt
- Button text: Arial, 12pt, Bold, White

### Components

1. **Header Bar**
   - EuroCaps logo (left-aligned)
   - Application title
   - User profile dropdown (right-aligned)
   - Settings icon (right-aligned)

2. **Navigation Menu**
   - Vertical sidebar with menu items
   - "Products" highlighted
   - Icons for each menu item

3. **Search and Filter Section**
   - Search input with search icon
   - Type filter dropdown (Espresso, Lungo, Ristretto, Flavored)
   - Size filter dropdown (10, 20, 44)
   - Sort dropdown (Name, Type, Size)
   - Reset filters button

4. **Product Grid**
   - Cards arranged in a responsive grid (4 columns)
   - Each card contains:
     - Product image
     - Product name
     - Product type
     - Package size
     - Add button

5. **Pagination**
   - Previous/Next buttons
   - Page indicator
   - Items per page selector (optional)

## Interactions

1. **Search Functionality**
   - Real-time filtering as user types
   - Search across product name and description

2. **Filtering and Sorting**
   - Type filter to show specific product types
   - Size filter to show specific package sizes
   - Sort dropdown to order by different fields
   - Reset button clears all filters and search

3. **Product Actions**
   - Click on product card to view detailed information
   - "Add" button to quickly add to current order
     - If no order is in progress, prompt to create one
     - If order exists, add with default quantity (1)

4. **Pagination**
   - Previous/Next buttons navigate between pages
   - Disable Previous on first page
   - Disable Next on last page

## Accessibility Considerations
- Clear visual hierarchy
- Color is not the only indicator of product type
- Sufficient contrast for all text elements
- Alt text for all product images

## Notes for Implementation
- Consider adding quick view functionality
- Add quantity selector on hover/click
- For prototype: Use mock data and placeholder images
- Implement client-side pagination for the prototype
