# Order Items Screen - EuroCaps Ordering System

## Screen Layout

```
+---------------------------------------------------------------+
| [Logo] EuroCaps Ordering System           [User ▼] [Settings] |
+---------------------------------------------------------------+
| [≡ MENU]  | Add Products to Order - ORD-1089                  |
|           |                                                    |
| Dashboard | [Search Products...                       ] [🔍]  |
| Customers |                                                    |
| Products  | FILTERS:                                           |
| Orders    | Type: [All ▼] Size: [All ▼] [Sort: Name ▼] [Reset]|
| Reports   |                                                    |
|           | PRODUCT SELECTION                                  |
| Settings  | +------------------------------------------------+ |
| Logout    | |  [Image]  |  [Image]  |  [Image]  |  [Image]  | |
|           | |  Espresso |  Lungo    |  Ristretto|  Vanilla  | |
|           | |  Classic  |  Intense  |  Strong   |  Flavored | |
|           | |  Size: 10 |  Size: 20 |  Size: 10 |  Size: 20 | |
|           | |  Qty: [1▼]|  Qty: [1▼]|  Qty: [1▼]|  Qty: [1▼]| |
|           | |  [+ Add]  |  [+ Add]  |  [+ Add]  |  [+ Add]  | |
|           | +------------------------------------------------+ |
|           |                                                    |
|           | CURRENT ORDER ITEMS                                |
|           | +------------------------------------------------+ |
|           | | Product       | Type     | Size | Qty | Remove | |
|           | |------------------------------------------------| |
|           | | Espresso      | Espresso | 10   | 5   | [✖]    | |
|           | | Lungo Intense | Lungo    | 20   | 3   | [✖]    | |
|           | +------------------------------------------------+ |
|           |                                                    |
|           | [CONTINUE SHOPPING]           [DONE]              |
|           |                                                    |
+---------------------------------------------------------------+
```

## Design Elements

### Colors
- Header: Blue (#4a6fa5)
- Menu sidebar: Dark blue (#3a5a80)
- Background: Light gray (#f5f5f5)
- Product cards: White (#ffffff)
- Current items section: White (#ffffff)
- Add button: Green (#4caf50)
- Remove button: Red (#f44336)
- Continue button: Blue (#4a6fa5)
- Done button: Green (#4caf50)

### Typography
- Header: Arial, 16pt, Bold, White
- Menu items: Arial, 14pt, White
- Page title: Arial, 18pt, Bold, Dark gray
- Section titles: Arial, 16pt, Bold, Dark gray
- Product name: Arial, 14pt, Bold
- Product details: Arial, 12pt
- Button text: Arial, 12pt, Bold, White

### Components

1. **Header Bar**
   - EuroCaps logo (left-aligned)
   - Application title
   - User profile dropdown (right-aligned)
   - Settings icon (right-aligned)

2. **Navigation Menu**
   - Vertical sidebar with menu items
   - Icons for each menu item

3. **Search and Filter Section**
   - Search input with search icon
   - Type filter dropdown (Espresso, Lungo, Ristretto, Flavored)
   - Size filter dropdown (10, 20, 44)
   - Sort dropdown (Name, Type, Size)
   - Reset filters button

4. **Product Selection Grid**
   - Cards arranged in a responsive grid (4 columns)
   - Each card contains:
     - Product image
     - Product name
     - Product type
     - Package size
     - Quantity selector
     - Add button

5. **Current Order Items Section**
   - Table of already selected products
   - Columns: Product, Type, Size, Quantity, Remove
   - Remove button for each item

6. **Action Buttons**
   - "Continue Shopping" (secondary)
   - "Done" (primary)

## Interactions

1. **Search and Filtering**
   - Real-time filtering as user types
   - Filter dropdowns update product grid immediately
   - Reset button clears all filters and search

2. **Product Selection**
   - Quantity selector allows choosing 1-99 units
   - "Add" button adds product to current order items
     - If product already in order, update quantity instead
   - Visual feedback when product added

3. **Current Items Management**
   - Remove button removes item from order
   - Quantity can be adjusted directly in the table
   - Real-time update of order summary

4. **Navigation Actions**
   - "Continue Shopping" keeps current selections and loads more products
   - "Done" finalizes selections and returns to order screen
   - Prompt confirmation if navigating away with unsaved changes

## Validation Rules

1. **Quantity Validation**
   - Must be positive integers
   - Maximum quantity may be limited by available stock
   - Warning for unusually large quantities

2. **Product Compatibility**
   - Optional: Check for incompatible product combinations
   - Suggest complementary products

## Accessibility Considerations
- Clear visual hierarchy
- Sufficient contrast for all text elements
- Keyboard navigation for product selection
- Screen reader support for dynamic content updates

## Notes for Implementation
- Consider adding quick view functionality for product details
- Add recently ordered products section at top
- For prototype: Use mock data for products
- Implement basic validation for demonstration purposes
- Consider adding a running total of items and cost
